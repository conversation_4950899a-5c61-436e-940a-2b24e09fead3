import sys
import os
from pathlib import Path
import numpy as np
import pandas as pd
import pytest

# Ensure the src directory is on the Python path for tests
ROOT = Path(__file__).resolve().parents[1]
SRC = ROOT / "src"
if str(SRC) not in sys.path:
    sys.path.insert(0, str(SRC))


# Настройка Numba для параллельного выполнения тестов
def pytest_sessionstart(session):
    """Настройка для работы с pytest-xdist."""
    if os.getenv("PYTEST_XDIST_WORKER"):
        # Ограничиваем потоки Numba для воркеров xdist
        try:
            from numba import set_num_threads
            set_num_threads(1)  # по 1 потоку на воркер
        except ImportError:
            pass


@pytest.fixture(scope="session")
def small_prices_df():
    """Малый набор синтетических данных для быстрых тестов."""
    np.random.seed(42)
    n = 250  # вместо 2-5k
    dates = pd.date_range("2024-01-01", periods=n, freq="15min")
    base = np.random.randn(n).cumsum()
    y = 100 * np.exp(base * 0.01)
    x = 95 * np.exp((0.8*base + np.random.randn(n)*0.007).cumsum()*0.01)
    return pd.DataFrame({"y": y, "x": x}, index=dates)


@pytest.fixture
def fast_study(tmp_path):
    """Быстрая Optuna study для тестов."""
    try:
        import optuna
        storage = f"sqlite:///{tmp_path/'study.db'}"  # локально и изолированно
        return optuna.create_study(
            storage=storage,
            load_if_exists=True,
            sampler=optuna.samplers.RandomSampler(seed=0),
            direction="maximize",
        )
    except ImportError:
        pytest.skip("Optuna not available")


@pytest.fixture(scope="session", autouse=True)
def _setup_global_cache():
    """Инициализация и очистка глобального кэша."""
    try:
        from coint2.core.global_rolling_cache import initialize_global_rolling_cache, cleanup_global_rolling_cache
        # Минимальная конфигурация для тестов
        test_config = {
            'rolling_window': 30,
            'volatility_lookback': 96,
            'correlation_window': 720,
            'hurst_window': 720,
            'variance_ratio_window': 480
        }
        initialize_global_rolling_cache(test_config)
        yield
        cleanup_global_rolling_cache()
    except (ImportError, Exception):
        # Если модуль не найден или ошибка инициализации, просто пропускаем
        yield


@pytest.fixture
def minimal_config():
    """Минимальная конфигурация для быстрых тестов."""
    return {
        "signals": {
            "zscore_threshold": 2.0,
            "zscore_exit": 0.5,
            "rolling_window": 20
        },
        "portfolio": {
            "max_active_positions": 5,
            "risk_per_position_pct": 0.01,
            "max_position_size_pct": 0.1
        },
        "costs": {
            "commission_pct": 0.001,
            "slippage_pct": 0.0005
        },
        "normalization": {
            "normalization_method": "minmax",
            "min_history_ratio": 0.6
        }
    }