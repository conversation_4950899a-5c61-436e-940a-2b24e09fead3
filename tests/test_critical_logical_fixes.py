#!/usr/bin/env python3
"""
Тесты для проверки исправлений критических и логических ошибок в Optuna оптимизации.
Проверяет корректность всех исправленных проблем.
"""

import pytest
import unittest
import sys
import numpy as np
import pandas as pd
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock
import optuna
import yaml

# Добавляем src в путь для импорта
sys.path.append(str(Path(__file__).parent.parent / "src"))

from src.optimiser.fast_objective import FastWalkForwardObjective
from src.optimiser.metric_utils import validate_params, extract_sharpe, normalize_params
from src.optimiser.constants import (
    PENALTY_SOFT, PENALTY_HARD, INTERMEDIATE_REPORT_INTERVAL,
    DD_PENALTY_SOFT_MULTIPLIER, DD_PENALTY_HARD_MULTIPLIER,
    MAX_DRAWDOWN_SOFT_THRESHOLD, MAX_DRAWDOWN_HARD_THRESHOLD
)


class TestCriticalFixes:
    """Тесты для критических исправлений."""
    
    def test_no_duplicate_optuna_import(self):
        """Проверяет отсутствие дублирования импорта optuna."""
        # Читаем файл и проверяем количество импортов optuna
        with open("src/optimiser/fast_objective.py", "r") as f:
            content = f.read()

        # Ищем только прямые импорты optuna (не from optuna import ...)
        import_lines = [line for line in content.split('\n')
                       if line.strip().startswith('import optuna') and 'from' not in line]
        assert len(import_lines) == 1, f"Найдено {len(import_lines)} импортов optuna, должен быть 1"
    
    def test_pairs_skipped_variables_defined(self):
        """Проверяет что переменные pairs_skipped и skipped_ratio определены в коде."""
        # Читаем файл и проверяем что переменные определены перед использованием
        with open("src/optimiser/fast_objective.py", "r") as f:
            content = f.read()

        lines = content.split('\n')

        # Ищем строки где используются pairs_skipped и skipped_ratio
        pairs_skipped_usage = []
        skipped_ratio_usage = []
        pairs_skipped_definition = []
        skipped_ratio_definition = []

        for i, line in enumerate(lines):
            if 'pairs_skipped' in line:
                if '=' in line and not line.strip().startswith('#'):
                    if 'pairs_skipped =' in line or 'pairs_skipped=' in line:
                        pairs_skipped_definition.append(i+1)
                    else:
                        pairs_skipped_usage.append(i+1)

            if 'skipped_ratio' in line:
                if '=' in line and not line.strip().startswith('#'):
                    if 'skipped_ratio =' in line or 'skipped_ratio=' in line:
                        skipped_ratio_definition.append(i+1)
                    else:
                        skipped_ratio_usage.append(i+1)

        # Проверяем что переменные определены перед использованием
        if pairs_skipped_usage:
            assert pairs_skipped_definition, "pairs_skipped используется но не определена"
            assert min(pairs_skipped_definition) < min(pairs_skipped_usage), \
                "pairs_skipped должна быть определена перед использованием"

        if skipped_ratio_usage:
            assert skipped_ratio_definition, "skipped_ratio используется но не определена"
            assert min(skipped_ratio_definition) < min(skipped_ratio_usage), \
                "skipped_ratio должна быть определена перед использованием"
    
    def test_dd_penalty_no_duplication(self):
        """Проверяет отсутствие дублирования логики расчета dd_penalty."""
        # Читаем файл и проверяем что dd_penalty рассчитывается только один раз
        with open("src/optimiser/fast_objective.py", "r") as f:
            content = f.read()
        
        # Ищем строки с расчетом dd_penalty
        lines = content.split('\n')
        dd_penalty_calculations = []
        
        for i, line in enumerate(lines):
            if 'dd_penalty' in line and ('=' in line or '+=' in line) and not line.strip().startswith('#'):
                dd_penalty_calculations.append((i+1, line.strip()))
        
        # Должен быть только один блок расчета dd_penalty
        initialization_lines = [line for line in dd_penalty_calculations if 'dd_penalty = 0' in line[1]]
        assert len(initialization_lines) == 1, f"Найдено {len(initialization_lines)} инициализаций dd_penalty, должна быть 1"
    
    def test_no_unreachable_code_after_raise(self):
        """Проверяет отсутствие недостижимого кода после raise."""
        with open("src/optimiser/fast_objective.py", "r") as f:
            content = f.read()
        
        lines = content.split('\n')
        
        for i, line in enumerate(lines):
            if 'raise optuna.TrialPruned' in line:
                # Проверяем следующие строки до конца блока
                j = i + 1
                while j < len(lines) and lines[j].strip():
                    next_line = lines[j].strip()
                    # Пропускаем комментарии и пустые строки
                    if next_line.startswith('#') or not next_line:
                        j += 1
                        continue
                    # Если это не начало нового блока (except, finally, etc.)
                    if not any(next_line.startswith(keyword) for keyword in ['except', 'finally', 'else', 'elif']):
                        # Проверяем что это не return после raise в том же блоке
                        if next_line.startswith('return') and 'ИСПРАВЛЕНО' in next_line:
                            # Это исправленный код - проверяем что есть комментарий о том что это для не-Optuna случая
                            assert 'не Optuna' in lines[j-1] or 'не Optuna' in lines[j], \
                                f"Строка {j+1}: return после raise должен быть только для не-Optuna случая"
                    j += 1


class TestLogicalFixes:
    """Тесты для логических исправлений."""
    
    def test_zscore_exit_validation_fixed(self):
        """Проверяет исправленную логику валидации zscore_exit."""
        # Тест 1: Правильные параметры должны проходить валидацию
        valid_params = {
            'zscore_threshold': 2.0,
            'zscore_exit': 0.5
        }
        result = validate_params(valid_params)
        assert result['zscore_threshold'] == 2.0
        assert result['zscore_exit'] == 0.5

        # Тест 2: zscore_exit >= zscore_threshold должен быть автоматически исправлен
        # (validate_params исправляет, а не выбрасывает ошибку)
        result = validate_params({
            'zscore_threshold': 2.0,
            'zscore_exit': 2.0  # Равен threshold
        })
        assert result['zscore_exit'] < result['zscore_threshold'], \
            "zscore_exit должен быть автоматически исправлен"

        result = validate_params({
            'zscore_threshold': 2.0,
            'zscore_exit': 2.5  # Больше threshold
        })
        assert result['zscore_exit'] < result['zscore_threshold'], \
            "zscore_exit должен быть автоматически исправлен"

        # Тест 3: Отрицательный zscore_exit должен быть исправлен на 0
        result = validate_params({
            'zscore_threshold': 2.0,
            'zscore_exit': -0.5
        })
        assert result['zscore_exit'] == 0.0, \
            "Отрицательный zscore_exit должен быть исправлен на 0"

        # Тест 4: Проверяем что в _validate_cross_parameter_constraints есть правильная проверка
        # Этот тест проверяет исправленную логику в cross-validation
        with pytest.raises(ValueError, match="zscore_exit должен быть >= 0"):
            from src.optimiser.metric_utils import _validate_cross_parameter_constraints
            _validate_cross_parameter_constraints({
                'zscore_threshold': 2.0,
                'zscore_exit': -0.5
            })
    
    def test_none_values_handling_fixed(self):
        """Проверяет исправленную обработку None значений."""
        # Тест с None значениями - должны быть заменены на defaults
        params_with_none = {
            'stop_loss_multiplier': None,
            'max_active_positions': None,
            'max_position_size_pct': None,
            'risk_per_position_pct': None
        }
        
        result = validate_params(params_with_none)
        
        assert result['stop_loss_multiplier'] == 2.0
        assert result['max_active_positions'] == 10
        assert result['max_position_size_pct'] == 1.0
        assert result['risk_per_position_pct'] == 0.01
    
    def test_trading_days_calculation_fixed(self):
        """Проверяет исправленный расчет торговых дней в коде."""
        # Читаем файл и проверяем логику расчета торговых дней
        with open("src/optimiser/fast_objective.py", "r") as f:
            content = f.read()

        # Проверяем что есть правильная логика для расчета торговых дней
        assert "len(daily_returns)" in content, \
            "Должен использоваться len(daily_returns) для расчета торговых дней"

        assert "calendar_days * 0.7" in content, \
            "Должна быть оценка торговых дней как 70% от календарных"

        # Проверяем что нет старой неправильной логики
        lines = content.split('\n')
        for line in lines:
            if 'trading_days = max(1, len(daily_returns) if len(daily_returns) > 0 else self.base_config.walk_forward.testing_period_days)' in line:
                pytest.fail("Найдена старая неправильная логика расчета торговых дней")


class TestPerformanceOptimizations:
    """Тесты для оптимизаций производительности."""
    
    def test_intermediate_report_interval_optimized(self):
        """Проверяет оптимизированную частоту промежуточных отчетов."""
        assert INTERMEDIATE_REPORT_INTERVAL == 5, \
            f"INTERMEDIATE_REPORT_INTERVAL должен быть 5, получен: {INTERMEDIATE_REPORT_INTERVAL}"
    
    def test_pruner_parameters_standardized(self):
        """Проверяет стандартизацию параметров pruner во всех скриптах."""
        # Проверяем fast_optimize.py
        with open("scripts/fast_optimize.py", "r") as f:
            content = f.read()
            assert "interval_steps=5" in content, "fast_optimize.py должен использовать interval_steps=5"
            assert "n_warmup_steps=30" in content, "fast_optimize.py должен использовать n_warmup_steps=30"
        
        # Проверяем bp_optimize.py
        with open("scripts/bp_optimize.py", "r") as f:
            content = f.read()
            assert "interval_steps=5" in content, "bp_optimize.py должен использовать interval_steps=5"
            assert "n_warmup_steps=30" in content, "bp_optimize.py должен использовать n_warmup_steps=30"
        
        # Проверяем run_optimization.py
        with open("src/optimiser/run_optimization.py", "r") as f:
            content = f.read()
            assert "interval_steps=5" in content, "run_optimization.py должен использовать interval_steps=5"


def mock_open_yaml(data):
    """Helper для мокирования yaml.safe_load."""
    from unittest.mock import mock_open
    import json
    return mock_open(read_data=yaml.dump(data))


class TestWinRateFix(unittest.TestCase):
    """Тесты для исправления ошибки win_rate."""

    def test_win_rate_variable_fix(self):
        """Тест 10: Исправление ошибки NameError: name 'win_rate' is not defined."""
        print("\n=== Тест 10: Исправление ошибки win_rate ===")

        # Определяем project_root для этого теста
        project_root = Path(__file__).parent.parent
        config_path = project_root / "configs" / "main_2024.yaml"
        search_space_path = project_root / "configs" / "search_space_fast.yaml"

        # Создаем objective
        objective = FastWalkForwardObjective(
            str(config_path),
            str(search_space_path)
        )

        # Мокаем бэктест
        mock_metrics = {
            'sharpe_ratio_abs': 1.2,
            'total_trades': 100,
            'max_drawdown': 0.15,
            'total_pnl': 1200.0,
            'total_return_pct': 0.12,
            'win_rate': 0.52,  # Важно: включаем win_rate в metrics
            'avg_trade_size': 400.0,
            'avg_hold_time': 20.0
        }

        with patch.object(objective, '_run_fast_backtest') as mock_backtest, \
             patch.object(objective, '_run_fast_backtest_with_reports') as mock_backtest_reports:
            mock_backtest.return_value = mock_metrics
            mock_backtest_reports.return_value = mock_metrics

            # Создаем реальный trial
            study = optuna.create_study(direction='maximize')

            def test_objective_func(trial):
                # Должно работать без NameError
                result = objective(trial)
                return result

            # Запускаем один trial - не должно быть NameError
            study.optimize(test_objective_func, n_trials=1)

            # Проверяем что trial завершился успешно
            assert len(study.trials) == 1, "Должен быть 1 trial"
            trial = study.trials[0]
            assert trial.state == optuna.trial.TrialState.COMPLETE, f"Trial должен быть завершен: {trial.state}"

            # Проверяем что win_rate сохранен в метриках
            metrics = trial.user_attrs.get("metrics", {})
            assert "win_rate" in metrics, "win_rate должен быть в метриках trial"
            assert isinstance(metrics["win_rate"], (int, float)), "win_rate должен быть числом"

        print("✓ Ошибка NameError: name 'win_rate' is not defined исправлена")


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
