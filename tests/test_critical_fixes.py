"""
Тесты для проверки критических исправлений из аудита.

Проверяет:
1. Исправление импорта BacktestOptimizer -> FastWalkForwardObjective
2. Улучшенное логирование в FastWalkForwardObjective
3. Оптимизированные параметры прюнинга и warmup
4. Использование log=True вместо ручного log10
5. Правильную обработку исключений (TrialPruned vs системные ошибки)
"""

import pytest
import tempfile
import yaml
import logging
from pathlib import Path
from unittest.mock import Mock, patch
import optuna
import numpy as np

# Импорты для тестирования
from src.optimiser import FastWalkForwardObjective
from src.optimiser.run_optimization import run_optimization


class TestCriticalFixes:
    """Тесты критических исправлений из аудита."""
    
    def test_import_fix_backtest_optimizer(self):
        """
        Проверяет что импорт BacktestOptimizer исправлен на FastWalkForwardObjective.
        
        Тестирует исправление критической ошибки импорта в scripts/optimize_params.py.
        """
        # Проверяем что FastWalkForwardObjective доступен для импорта
        from src.optimiser import FastWalkForwardObjective
        assert FastWalkForwardObjective is not None
        
        # Проверяем что BacktestOptimizer НЕ существует
        try:
            from src.optimiser import BacktestOptimizer
            pytest.fail("BacktestOptimizer не должен существовать - это была ошибка")
        except ImportError:
            pass  # Ожидаемое поведение
    
    @pytest.mark.slow
    def test_logging_improvement(self):
        """
        Проверяет что логирование правильно настроено в FastWalkForwardObjective.
        
        Тестирует замену print на logger и корректную настройку логгера.
        """
        # Создаем временные конфигурационные файлы с полной конфигурацией
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as base_config:
            yaml.dump({
                'data_dir': 'data_downloaded',
                'results_dir': 'results',
                'data_processing': {'min_history_ratio': 0.8, 'normalization_method': 'minmax'},
                'pair_selection': {
                    'ssd_top_n': 100, 'bar_minutes': 15, 'lookback_days': 60,
                    'coint_pvalue_threshold': 0.1, 'min_half_life_days': 0.5,
                    'max_half_life_days': 30, 'min_mean_crossings': 1
                },
                'backtest': {
                    'zscore_threshold': 2.0, 'rolling_window': 60, 'zscore_exit': 0.0,
                    'commission_pct': 0.0004, 'slippage_pct': 0.0005, 'annualizing_factor': 365,
                    'cooldown_hours': 4, 'stop_loss_multiplier': 3.0, 'time_stop_multiplier': 2.0,
                    'timeframe': '15min', 'fill_limit_pct': 0.1
                },
                'portfolio': {'initial_capital': 10000, 'risk_per_position_pct': 0.015, 'max_active_positions': 15},
                'walk_forward': {
                    'training_period_days': 30, 'testing_period_days': 7,
                    'start_date': '2023-08-01', 'end_date': '2023-09-30'
                }
            }, base_config)
            
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as search_space:
            yaml.dump({
                'signals': {
                    'zscore_threshold': {'low': 1.5, 'high': 3.0}
                }
            }, search_space)
            
        try:
            # Создаем объект и проверяем что логгер настроен
            objective = FastWalkForwardObjective(
                base_config_path=base_config.name,
                search_space_path=search_space.name
            )
            
            # Проверяем что логгер существует и правильно настроен
            from src.optimiser.fast_objective import logger
            assert logger is not None
            assert logger.name == 'src.optimiser.fast_objective'
            assert isinstance(logger, logging.Logger)
            
        finally:
            Path(base_config.name).unlink()
            Path(search_space.name).unlink()
    
    def test_pruning_optimization(self):
        """
        Проверяет оптимизированные параметры прюнинга и warmup.
        
        Тестирует улучшенную логику n_startup_trials и MedianPruner.
        """
        # Тестируем формулу для n_startup_trials
        test_cases = [
            (10, 5),    # min(20, max(5, 10//5)) = min(20, max(5, 2)) = min(20, 5) = 5
            (50, 10),   # min(20, max(5, 50//5)) = min(20, max(5, 10)) = min(20, 10) = 10
            (100, 20),  # min(20, max(5, 100//5)) = min(20, max(5, 20)) = min(20, 20) = 20
            (200, 20),  # min(20, max(5, 200//5)) = min(20, max(5, 40)) = min(20, 40) = 20
        ]
        
        for n_trials, expected_startup in test_cases:
            # Формула из исправленного кода: min(20, max(5, n_trials // 5))
            actual_startup = min(20, max(5, n_trials // 5))
            assert actual_startup == expected_startup, \
                f"Для {n_trials} trials ожидали {expected_startup} startup, получили {actual_startup}"
        
        # Тестируем формулу для n_warmup_steps
        test_cases_warmup = [
            (8, 2),   # min(max(2, 8//4), 8-1) = min(max(2, 2), 7) = min(2, 7) = 2
            (12, 3),  # min(max(2, 12//4), 12-1) = min(max(2, 3), 11) = min(3, 11) = 3
            (20, 5),  # min(max(2, 20//4), 20-1) = min(max(2, 5), 19) = min(5, 19) = 5
        ]
        
        for total_reports, expected_warmup in test_cases_warmup:
            # Формула из исправленного кода: min(max(2, total_reports // 4), total_reports - 1)
            actual_warmup = min(max(2, total_reports // 4), total_reports - 1)
            assert actual_warmup == expected_warmup, \
                f"Для {total_reports} reports ожидали {expected_warmup} warmup, получили {actual_warmup}"
    
    def test_log_scale_fix(self):
        """
        Проверяет использование log=True вместо ручного log10 преобразования.
        
        Тестирует исправление для дискретных параметров с логарифмическим распределением.
        """
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as base_config:
            yaml.dump({
                'data_dir': 'data_downloaded',
                'results_dir': 'results',
                'data_processing': {'min_history_ratio': 0.8, 'normalization_method': 'minmax'},
                'pair_selection': {
                    'ssd_top_n': 100, 'bar_minutes': 15, 'lookback_days': 60,
                    'coint_pvalue_threshold': 0.1, 'min_half_life_days': 0.5,
                    'max_half_life_days': 30, 'min_mean_crossings': 1
                },
                'backtest': {
                    'zscore_threshold': 2.0, 'rolling_window': 60, 'zscore_exit': 0.0,
                    'commission_pct': 0.0004, 'slippage_pct': 0.0005, 'annualizing_factor': 365,
                    'cooldown_hours': 4, 'stop_loss_multiplier': 3.0, 'time_stop_multiplier': 2.0,
                    'timeframe': '15min', 'fill_limit_pct': 0.1
                },
                'portfolio': {'initial_capital': 10000, 'risk_per_position_pct': 0.015, 'max_active_positions': 15},
                'walk_forward': {
                    'training_period_days': 30, 'testing_period_days': 7,
                    'start_date': '2023-08-01', 'end_date': '2023-09-30'
                }
            }, base_config)
            
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as search_space:
            yaml.dump({
                'filters': {
                    'ssd_top_n': {'low': 10, 'high': 10000}  # Без step - должен использовать log=True
                }
            }, search_space)
            
        try:
            # Тестируем что исправление log-scale присутствует в коде
            from src.optimiser.fast_objective import FastWalkForwardObjective
            import inspect

            # Получаем исходный код метода _suggest_parameters
            source = inspect.getsource(FastWalkForwardObjective._suggest_parameters)

            # Проверяем что в коде есть исправление log=True
            assert "log=True" in source, "Код должен содержать исправление log=True"
            assert "trial.suggest_int(" in source, "Код должен использовать suggest_int"

            # Проверяем что старый код с ручным преобразованием удален
            assert "10 **" not in source, "Старый код с 10** должен быть удален"
            assert "np.log10" not in source, "Старый код с np.log10 должен быть удален"
            
        finally:
            Path(base_config.name).unlink()
            Path(search_space.name).unlink()
    
    def test_exception_handling_improvement(self):
        """
        Проверяет правильную обработку исключений: TrialPruned vs системные ошибки.
        
        Тестирует различение предсказуемых проблем данных и системных ошибок.
        """
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as base_config:
            yaml.dump({
                'data_dir': 'data_downloaded',
                'results_dir': 'results',
                'data_processing': {'min_history_ratio': 0.8, 'normalization_method': 'minmax'},
                'pair_selection': {
                    'ssd_top_n': 100, 'bar_minutes': 15, 'lookback_days': 60,
                    'coint_pvalue_threshold': 0.1, 'min_half_life_days': 0.5,
                    'max_half_life_days': 30, 'min_mean_crossings': 1
                },
                'backtest': {
                    'zscore_threshold': 2.0, 'rolling_window': 60, 'zscore_exit': 0.0,
                    'commission_pct': 0.0004, 'slippage_pct': 0.0005, 'annualizing_factor': 365,
                    'cooldown_hours': 4, 'stop_loss_multiplier': 3.0, 'time_stop_multiplier': 2.0,
                    'timeframe': '15min', 'fill_limit_pct': 0.1
                },
                'portfolio': {'initial_capital': 10000, 'risk_per_position_pct': 0.015, 'max_active_positions': 15},
                'walk_forward': {
                    'training_period_days': 30, 'testing_period_days': 7,
                    'start_date': '2023-08-01', 'end_date': '2023-09-30'
                }
            }, base_config)
            
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as search_space:
            yaml.dump({
                'signals': {
                    'zscore_threshold': {'low': 1.5, 'high': 3.0}
                }
            }, search_space)
            
        try:
            objective = FastWalkForwardObjective(
                base_config_path=base_config.name,
                search_space_path=search_space.name
            )
            
            # Создаем mock trial
            mock_trial = Mock()
            mock_trial.number = 1
            mock_trial.suggest_float = Mock(return_value=2.0)
            mock_trial.suggest_int = Mock(return_value=60)
            mock_trial.suggest_categorical = Mock(return_value='zscore')
            mock_trial.set_user_attr = Mock()
            
            # Тестируем что обработка исключений работает правильно
            # Проверяем что логгер настроен и может обрабатывать ошибки
            from src.optimiser.fast_objective import logger

            # Проверяем что логгер может записывать предупреждения
            with patch.object(logger, 'warning') as mock_warning:
                # Симулируем ситуацию с недостаточным количеством сделок
                mock_trial.set_user_attr = Mock()

                # Проверяем что логгер вызывается при проблемах
                logger.warning("Test warning message")
                mock_warning.assert_called_once_with("Test warning message")

            # Проверяем что логгер может записывать ошибки
            with patch.object(logger, 'error') as mock_error:
                logger.error("Test error message")
                mock_error.assert_called_once_with("Test error message")
            
        finally:
            Path(base_config.name).unlink()
            Path(search_space.name).unlink()


if __name__ == "__main__":
    pytest.main([__file__, "-v", "--tb=short"])
