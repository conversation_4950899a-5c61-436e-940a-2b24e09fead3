#!/usr/bin/env python3
"""
ТЕСТЫ ДЛЯ ПРОВЕРКИ ИСПРАВЛЕНИЯ КРИТИЧЕСКИХ И ЛОГИЧЕСКИХ ОШИБОК В OPTUNA ОПТИМИЗАЦИИ

Проверяет:
1. Промежуточные отчеты используют накопленные данные (без lookahead bias)
2. Отсутствие недостижимого кода после raise optuna.TrialPruned
3. Правильная категоризация ошибок
4. Консистентность счетчиков в промежуточных отчетах
"""

import pytest
import numpy as np
import pandas as pd
import optuna
from unittest.mock import Mock, patch, MagicMock
import tempfile
import yaml
from pathlib import Path

from src.optimiser.fast_objective import FastWalkForwardObjective


class TestOptunaCriticalFixes:
    """Тесты исправления критических ошибок в Optuna оптимизации."""
    
    @pytest.fixture
    def mock_config(self):
        """Создает мок конфигурацию для тестов."""
        return {
            'data_dir': 'data_downloaded',
            'results_dir': 'results',
            'backtest': {
                'annualizing_factor': 365,
                'commission_pct': 0.0004,
                'slippage_pct': 0.0005,
                'zscore_threshold': 1.5,
                'rolling_window': 30,
                'timeframe': '15min',
                'stop_loss_multiplier': 3.0,
                'fill_limit_pct': 0.1,
                'zscore_exit': 0.0,
                'time_stop_multiplier': 2.0
            },
            'portfolio': {
                'initial_capital': 10000.0,
                'risk_per_position_pct': 0.015,
                'max_active_positions': 15
            },
            'pair_selection': {
                'bar_minutes': 15,
                'lookback_days': 60,
                'coint_pvalue_threshold': 0.1,
                'ssd_top_n': 50000,
                'min_half_life_days': 0.5,
                'max_half_life_days': 30,
                'min_mean_crossings': 1,
                'liquidity_usd_daily': 100000,
                'max_bid_ask_pct': 0.5,
                'max_avg_funding_pct': 0.05,
                'max_hurst_exponent': 0.5,
                'kpss_pvalue_threshold': 0.005,
                'pvalue_top_n': 500
            },
            'walk_forward': {
                'enabled': True,
                'start_date': '2023-08-01',
                'end_date': '2023-09-30',
                'training_period_days': 30,
                'testing_period_days': 60,
                'step_size_days': 30
            },
            'data_processing': {
                'normalization_method': 'minmax'
            },
            'normalization': {
                'method': 'log_returns'
            },
            'online_statistics': {
                'enabled': True
            },
            'signal_shift': {
                'enabled': True
            },
            'logging': {
                'debug_level': 'INFO'
            }
        }
    
    @pytest.fixture
    def mock_trial(self):
        """Создает мок Optuna trial."""
        trial = Mock()
        trial.suggest_float.return_value = 2.0
        trial.suggest_int.return_value = 10
        trial.should_prune.return_value = False
        trial.set_user_attr = Mock()
        trial.number = 1
        return trial
    
    def test_intermediate_reports_use_accumulated_data(self, mock_config, mock_trial):
        """
        КРИТИЧЕСКИЙ ТЕСТ: Промежуточные отчеты используют накопленные данные.
        
        Проверяет что каждый промежуточный отчет рассчитывается на всех данных
        до текущего шага включительно, а не только на данных текущего шага.
        """
        # Создаем мок данные для 3 шагов walk-forward
        mock_step_results = [
            {'pnls': [pd.Series([10, -5, 15], index=pd.date_range('2024-01-01', periods=3, freq='D'))]},
            {'pnls': [pd.Series([20, -10], index=pd.date_range('2024-01-04', periods=2, freq='D'))]},
            {'pnls': [pd.Series([5, 25, -15], index=pd.date_range('2024-01-06', periods=3, freq='D'))]}
        ]
        
        # Мокаем _run_fast_backtest_with_reports для возврата мок результатов
        def mock_backtest_with_reports(params, trial):
            # Симулируем промежуточные отчеты
            for i, step_result in enumerate(mock_step_results):
                if step_result['pnls']:
                    # Рассчитываем простой Sharpe для теста
                    pnl_data = step_result['pnls'][0]
                    if len(pnl_data) > 0:
                        sharpe = pnl_data.mean() / (pnl_data.std() + 1e-6) * np.sqrt(252)
                        trial.report(float(sharpe), step=i)

            # Возвращаем финальные метрики
            return {
                "sharpe_ratio_abs": 1.5,
                "total_trades": 10,
                "max_drawdown": 0.1,
                "positive_days_rate": 0.6
            }
            
            # Отслеживаем вызовы trial.report
            reported_data = []
            def capture_report(value, step):
                reported_data.append((step, value))
            mock_trial.report = capture_report
            
            # Создаем временный конфиг файл
            with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
                yaml.dump(mock_config, f)
                config_path = f.name
            
            try:
                objective = FastWalkForwardObjective(config_path, config_path)
                
                # Мокаем _suggest_parameters и _run_fast_backtest_with_reports
                with patch.object(objective, '_suggest_parameters', return_value={}):
                    with patch.object(objective, '_run_fast_backtest_with_reports', side_effect=mock_backtest_with_reports):
                        objective(mock_trial)
                
                # Проверяем что отчеты идут в правильном порядке
                assert len(reported_data) == 3, f"Ожидалось 3 отчета, получено {len(reported_data)}"
                
                for i, (step, sharpe) in enumerate(reported_data):
                    assert step == i, f"Неправильный номер шага: ожидался {i}, получен {step}"
                    assert isinstance(sharpe, float), f"Sharpe должен быть float, получен {type(sharpe)}"
                    assert not np.isnan(sharpe), f"Sharpe не должен быть NaN на шаге {i}"
                
                print("✅ Промежуточные отчеты используют накопленные данные")
                
            finally:
                Path(config_path).unlink()
    
    def test_no_unreachable_code_after_trial_pruned(self, mock_config):
        """
        КРИТИЧЕСКИЙ ТЕСТ: Отсутствие недостижимого кода после raise optuna.TrialPruned.
        
        Проверяет что после raise optuna.TrialPruned нет недостижимого кода.
        """
        # Тестируем валидацию параметров с невалидными данными
        invalid_params = {'zscore_threshold': -1.0}  # Невалидный параметр
        
        mock_trial = Mock()
        mock_trial.suggest_float.return_value = 2.0
        mock_trial.set_user_attr = Mock()
        mock_trial.number = 1
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(mock_config, f)
            config_path = f.name
        
        try:
            objective = FastWalkForwardObjective(config_path, config_path)
            
            # Мокаем _suggest_parameters чтобы вернуть невалидные параметры
            with patch.object(objective, '_suggest_parameters', return_value=invalid_params):
                with pytest.raises(optuna.TrialPruned) as exc_info:
                    objective(mock_trial)
                
                # Проверяем что исключение содержит правильное сообщение
                assert "Parameter validation failed" in str(exc_info.value)
                
                # Проверяем что set_user_attr был вызван
                mock_trial.set_user_attr.assert_called()
                
                print("✅ TrialPruned корректно обрабатывается без недостижимого кода")
                
        finally:
            Path(config_path).unlink()
    
    def test_error_categorization_consistency(self, mock_config, mock_trial):
        """
        КРИТИЧЕСКИЙ ТЕСТ: Правильная категоризация ошибок.
        
        Проверяет что ZeroDivisionError и другие вычислительные ошибки
        обрабатываются отдельно от проблем данных.
        """
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(mock_config, f)
            config_path = f.name
        
        try:
            objective = FastWalkForwardObjective(config_path, config_path)
            
            # Тестируем различные типы ошибок
            error_test_cases = [
                (ValueError("Invalid data"), "data_problem"),
                (KeyError("Missing column"), "data_problem"),
                (IndexError("Index out of range"), "data_problem"),
                (ZeroDivisionError("Division by zero"), "calculation_error"),
                (FloatingPointError("Floating point error"), "calculation_error"),
                (RuntimeError("System error"), "system_error")
            ]
            
            for error, expected_category in error_test_cases:
                mock_trial.set_user_attr.reset_mock()
                
                # Мокаем _run_fast_backtest_with_reports чтобы вызвать ошибку
                with patch.object(objective, '_run_fast_backtest_with_reports', side_effect=error):
                    with patch.object(objective, '_suggest_parameters', return_value={}):
                        try:
                            objective(mock_trial)
                        except (optuna.TrialPruned, Exception):
                            pass  # Ожидаем исключение
                
                # Проверяем что ошибка была правильно категоризована
                calls = mock_trial.set_user_attr.call_args_list
                error_type_calls = [call for call in calls if call[0][0] == "error_type"]
                
                if error_type_calls:
                    actual_category = error_type_calls[0][0][1]
                    assert actual_category == expected_category, \
                        f"Ошибка {type(error).__name__} должна быть категоризована как {expected_category}, получено {actual_category}"
                
            print("✅ Ошибки правильно категоризованы")
                
        finally:
            Path(config_path).unlink()
    
    def test_step_counter_consistency(self, mock_config, mock_trial):
        """
        КРИТИЧЕСКИЙ ТЕСТ: Консистентность счетчиков в промежуточных отчетах.
        
        Проверяет что номера шагов в trial.report соответствуют
        реальным шагам walk-forward анализа.
        """
        # Создаем данные для 5 шагов
        mock_step_results = []
        for i in range(5):
            step_data = {'pnls': [pd.Series([10 + i], index=pd.date_range(f'2024-01-{i+1:02d}', periods=1, freq='D'))]}
            mock_step_results.append(step_data)
        
        # Мокаем _run_fast_backtest_with_reports для возврата мок результатов
        def mock_backtest_with_reports(params, trial):
            # Симулируем промежуточные отчеты для каждого шага
            for i, step_result in enumerate(mock_step_results):
                if step_result['pnls']:
                    # Рассчитываем простой Sharpe для теста
                    pnl_data = step_result['pnls'][0]
                    if len(pnl_data) > 0:
                        sharpe = pnl_data.mean() / (pnl_data.std() + 1e-6) * np.sqrt(252)
                        trial.report(float(sharpe), step=i)

            # Возвращаем финальные метрики
            return {
                "sharpe_ratio_abs": 1.5,
                "total_trades": 10,
                "max_drawdown": 0.1,
                "positive_days_rate": 0.6
            }
            
            step_numbers = []
            def capture_step_number(value, step):
                step_numbers.append(step)
            mock_trial.report = capture_step_number
            
            with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
                yaml.dump(mock_config, f)
                config_path = f.name
            
            try:
                objective = FastWalkForwardObjective(config_path, config_path)
                
                with patch.object(objective, '_suggest_parameters', return_value={}):
                    with patch.object(objective, '_run_fast_backtest_with_reports', side_effect=mock_backtest_with_reports):
                        objective(mock_trial)
                
                # Проверяем что номера шагов идут последовательно
                expected_steps = list(range(len(mock_step_results)))
                assert step_numbers == expected_steps, \
                    f"Номера шагов должны быть {expected_steps}, получено {step_numbers}"
                
                print("✅ Счетчики шагов консистентны")
                
            finally:
                Path(config_path).unlink()


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
