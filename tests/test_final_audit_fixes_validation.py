#!/usr/bin/env python3
"""
ФИНАЛЬНЫЕ ТЕСТЫ ВАЛИДАЦИИ ИСПРАВЛЕНИЙ АУДИТА

Проверяет все критические исправления из детального аудита:
1. Look-ahead bias в нормализации данных ✓ (уже исправлено)
2. Правильное разделение данных train/test ✓ (уже исправлено)  
3. Правильная обработка pruning vs penalty ✓ (уже исправлено)
4. Воспроизводимость через seeding ✓ (уже исправлено)
5. Параметры-зависимости (zscore_exit < zscore_threshold) ✓ (уже исправлено)
6. Отсутствие дублированных расчетов ✓ (уже исправлено)
"""

import pytest
import numpy as np
import pandas as pd
import optuna
import yaml
import sys
from pathlib import Path
from unittest.mock import patch, MagicMock
import tempfile
import os

# Добавляем путь к модулям проекта
sys.path.append(str(Path(__file__).parent.parent / "src"))

from coint2.core.normalization_improvements import (
    compute_normalization_params, 
    apply_normalization_with_params,
    preprocess_and_normalize_data
)
from optimiser.fast_objective import FastWalkForwardObjective
from optimiser.metric_utils import validate_params
from optimiser.constants import PENALTY_SOFT, PENALTY_HARD
from optimiser.run_optimization import run_optimization


def create_full_test_config():
    """Создает полную тестовую конфигурацию со всеми необходимыми полями"""
    return {
        'data_dir': 'data_downloaded',
        'results_dir': 'results',
        'walk_forward': {
            'enabled': True,
            'start_date': '2023-08-01',
            'end_date': '2023-09-30',
            'training_period_days': 30,
            'testing_period_days': 7,
            'step_days': 7,
            'step_size_days': 30,
            'min_training_samples': 100,
            'refit_frequency': 'weekly'
        },
        'backtest': {
            'timeframe': '15min',
            'rolling_window': 30,
            'zscore_threshold': 1.5,
            'zscore_entry_threshold': 1.5,
            'zscore_exit': 0.0,
            'stop_loss_multiplier': 3.0,
            'time_stop_multiplier': 2.0,
            'fill_limit_pct': 0.1,
            'commission_pct': 0.0004,
            'slippage_pct': 0.0005,
            'fee_maker': 0.0002,
            'fee_taker': 0.0004,
            'slippage_bps': 2.0,
            'half_spread_bps': 1.5,
            'slippage_stress_multiplier': 1.0,
            'always_model_slippage': True,
            'annualizing_factor': 365,
            'cooldown_hours': 4,
            'wait_for_candle_close': True,
            'min_spread_move_sigma': 0.1,
            'min_position_hold_minutes': 60,
            'anti_churn_cooldown_minutes': 60,
            'flat_zscore_threshold': 0.5,
            'pair_stop_loss_usd': 75.0,
            'pair_stop_loss_zscore': 3.0,
            'portfolio_daily_stop_pct': 0.02,
            'enable_funding_time_filter': True,
            'funding_blackout_minutes': 30,
            'funding_reset_hours': [0, 8, 16],
            'enable_macro_event_filter': True,
            'macro_blackout_minutes': 30,
            'enable_pair_quarantine': False,
            'quarantine_pnl_threshold_sigma': 3.0,
            'quarantine_drawdown_threshold_pct': 0.08,
            'quarantine_period_days': 7,
            'quarantine_rolling_window_days': 30,
            'enable_realistic_costs': True,
            'commission_rate_per_leg': 0.0004,
            'slippage_half_spread_multiplier': 2.0,
            'funding_cost_enabled': True,
            'beta_recalc_frequency_hours': 48,
            'beta_window_days_min': 60,
            'beta_window_days_max': 120,
            'use_ols_beta_sizing': True,
            'market_regime_detection': True,
            'hurst_window': 720,
            'hurst_trending_threshold': 0.5,
            'variance_ratio_window': 480,
            'variance_ratio_trending_min': 1.2,
            'variance_ratio_mean_reverting_max': 0.8,
            'structural_break_protection': True,
            'cointegration_test_frequency': 2688,
            'adf_pvalue_threshold': 0.05,
            'exclusion_period_days': 30,
            'max_half_life_days': 14,
            'min_correlation_threshold': 0.6,
            'correlation_window': 720,
            'regime_check_frequency': 192,
            'use_market_regime_cache': True,
            'adf_check_frequency': 5376,
            'cache_cleanup_frequency': 1000,
            'lazy_adf_threshold': 0.1,
            'hurst_neutral_band': 0.05,
            'vr_neutral_band': 0.2,
            'n_jobs': -1,
            'use_exponential_weighted_correlation': True,
            'ew_correlation_alpha': 0.1,
            'use_kelly_sizing': True,
            'max_kelly_fraction': 0.25,
            'volatility_lookback': 96,
            'adaptive_thresholds': True,
            'var_confidence': 0.05,
            'max_var_multiplier': 3.0
        },
        'portfolio': {
            'initial_capital': 100000,
            'risk_per_position_pct': 0.015,
            'max_active_positions': 15,
            'max_margin_usage': 0.5,
            'volatility_based_sizing': True,
            'volatility_lookback_hours': 24,
            'min_position_size_pct': 0.005,
            'max_position_size_pct': 0.02,
            'volatility_adjustment_factor': 2.0
        },
        'pair_selection': {
            'lookback_days': 60,
            'coint_pvalue_threshold': 0.1,
            'ssd_top_n': 50000,
            'min_half_life_days': 0.5,
            'max_half_life_days': 30,
            'min_mean_crossings': 1,
            'adaptive_quantiles': False,
            'bar_minutes': 15,
            'liquidity_usd_daily': 100000,
            'max_bid_ask_pct': 0.5,
            'max_avg_funding_pct': 0.05,
            'save_filter_reasons': True,
            'max_hurst_exponent': 0.5,
            'kpss_pvalue_threshold': 0.005,
            'pvalue_top_n': 500,
            'save_std_histogram': True,
            'enable_pair_tradeability_filter': True,
            'min_volume_usd_24h': 200000,
            'min_days_live': 7,
            'max_funding_rate_abs': 0.001,
            'max_tick_size_pct': 0.002,
            'max_half_life_hours': 720.0
        },
        'data_processing': {
            'normalization_method': 'minmax',
            'fill_method': 'linear',
            'min_history_ratio': 0.8,
            'handle_constant': True
        },
        'normalization': {
            'method': 'log_returns',
            'fit_on_training_only': True,
            'prevent_refit_on_test': True,
            'log_returns_base': 'natural'
        },
        'online_statistics': {
            'enabled': True,
            'volatility_method': 'ewm',
            'ewm_alpha': 0.05,
            'kelly_lookback_trades': 50,
            'adaptive_threshold_lookback': 96,
            'beta_recalc_online': True
        },
        'signal_shift': {
            'enabled': True,
            'shift_periods': 1,
            'skip_last_bar': True,
            'signal_delay_minutes': 0
        },
        'max_shards': None,
        'filter_params': {
            'min_beta': 0.05,
            'max_beta': 20.0,
            'min_half_life_days': 0.1,
            'max_half_life_days': 252,
            'max_hurst_exponent': 0.6,
            'min_mean_crossings': 2
        },
        'logging': {
            'trade_details': True,
            'debug_level': 'INFO'
        }
    }


class TestAuditFixesValidation:
    """Финальные тесты валидации всех исправлений из аудита."""

    def test_1_normalization_lookahead_bias_fixed(self):
        """
        КРИТИЧЕСКИЙ ТЕСТ 1: Look-ahead bias в нормализации исправлен
        
        Проверяет что:
        - compute_normalization_params использует только тренировочные данные
        - apply_normalization_with_params применяет параметры к тестовым данным
        - Нет утечки информации из будущего
        """
        print("\n🔍 ТЕСТ 1: Look-ahead bias в нормализации исправлен")
        
        # Создаем тестовые данные с явным трендом
        np.random.seed(42)
        dates = pd.date_range('2023-01-01', periods=200, freq='15min')
        
        # Тренировочные данные: низкие значения
        train_data = pd.DataFrame({
            'AAPL': np.random.normal(100, 5, 100),  # Среднее 100
            'MSFT': np.random.normal(200, 10, 100)  # Среднее 200
        }, index=dates[:100])
        
        # Тестовые данные: высокие значения (сдвиг вверх)
        test_data = pd.DataFrame({
            'AAPL': np.random.normal(150, 5, 100),  # Среднее 150 (сдвиг +50)
            'MSFT': np.random.normal(300, 10, 100)  # Среднее 300 (сдвиг +100)
        }, index=dates[100:])
        
        full_data = pd.concat([train_data, test_data])
        
        # ПРАВИЛЬНЫЙ ПОДХОД: Параметры только на тренировочных данных
        norm_params = compute_normalization_params(train_data, norm_method='minmax')
        normalized_test = apply_normalization_with_params(test_data, norm_params, norm_method='minmax')
        
        # НЕПРАВИЛЬНЫЙ ПОДХОД: Параметры на всех данных (look-ahead bias)
        old_normalized, _ = preprocess_and_normalize_data(full_data, norm_method='minmax')
        old_test_part = old_normalized.iloc[100:]
        
        # Проверяем что подходы дают разные результаты
        diff = np.abs(normalized_test.values - old_test_part.values).mean()
        assert diff > 0.1, f"Разница между подходами должна быть значительной, получено: {diff:.6f}"
        
        # Проверяем что новый подход не использует информацию из тестовых данных
        train_min = train_data.min()
        train_max = train_data.max()
        
        # В правильном подходе параметры должны быть основаны только на тренировочных данных
        assert norm_params['AAPL']['min'] == train_min['AAPL'], "Min параметр должен быть из тренировочных данных"
        assert norm_params['AAPL']['max'] == train_max['AAPL'], "Max параметр должен быть из тренировочных данных"
        
        print("✅ Look-ahead bias в нормализации исправлен")

    def test_2_data_separation_train_test_fixed(self):
        """
        КРИТИЧЕСКИЙ ТЕСТ 2: Правильное разделение данных train/test
        
        Проверяет что:
        - Данные правильно разделяются на тренировочные и тестовые
        - Нет перекрытия между периодами
        - Используются правильные срезы данных
        """
        print("\n🔍 ТЕСТ 2: Правильное разделение данных train/test")
        
        # Создаем временный конфиг
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            config = create_full_test_config()
            yaml.dump(config, f)
            config_path = f.name
        
        # Создаем временный search_space файл
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            search_space = {
                'signals': {
                    'zscore_threshold': {'low': 1.0, 'high': 2.0},
                    'zscore_exit': {'low': 0.0, 'high': 1.0}
                }
            }
            yaml.dump(search_space, f)
            search_space_path = f.name

        try:
            # Создаем объект с мокированием загрузки данных и отбора пар
            with patch('optimiser.fast_objective.load_master_dataset') as mock_load, \
                 patch.object(Path, 'exists', return_value=True), \
                 patch('pandas.read_csv') as mock_read_csv:

                # Мокаем загрузку данных
                dates = pd.date_range('2023-01-01', periods=1000, freq='15min')
                mock_data = pd.DataFrame({
                    'timestamp': dates,
                    'symbol': ['AAPL'] * 500 + ['MSFT'] * 500,
                    'close': np.random.normal(100, 10, 1000)
                })
                mock_load.return_value = mock_data

                # Мокаем предварительно отобранные пары
                mock_pairs = pd.DataFrame({
                    'symbol_1': ['AAPL'],
                    'symbol_2': ['MSFT'],
                    'coint_pvalue': [0.01],
                    'half_life_days': [5.0]
                })
                mock_read_csv.return_value = mock_pairs

                objective = FastWalkForwardObjective(config_path, search_space_path)
                
                # Тестируем метод загрузки данных
                training_start = pd.Timestamp('2023-01-01')
                training_end = pd.Timestamp('2023-01-30')
                testing_start = pd.Timestamp('2023-01-31')
                testing_end = pd.Timestamp('2023-02-06')
                
                data_result = objective._load_data_for_step(
                    training_start, training_end, testing_start, testing_end
                )
                
                # Проверяем что данные правильно разделены
                assert 'training_data' in data_result, "Должны быть тренировочные данные"
                assert 'testing_data' in data_result, "Должны быть тестовые данные"
                
                train_data = data_result['training_data']
                test_data = data_result['testing_data']
                
                # Проверяем отсутствие перекрытия
                if not train_data.empty and not test_data.empty:
                    train_max_date = train_data.index.max()
                    test_min_date = test_data.index.min()
                    assert train_max_date < test_min_date, f"Перекрытие данных: train_max={train_max_date}, test_min={test_min_date}"
                
                print("✅ Правильное разделение данных train/test")
                
        finally:
            os.unlink(config_path)
            os.unlink(search_space_path)

    def test_3_pruning_vs_penalty_strategy_fixed(self):
        """
        КРИТИЧЕСКИЙ ТЕСТ 3: Правильная стратегия pruning vs penalty
        
        Проверяет что:
        - Критические ошибки используют TrialPruned
        - Мягкие штрафы используются только для non-Optuna объектов
        - Системные ошибки правильно обрабатываются
        """
        print("\n🔍 ТЕСТ 3: Правильная стратегия pruning vs penalty")
        
        # Создаем временный конфиг
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            config = create_full_test_config()
            yaml.dump(config, f)
            config_path = f.name

        # Создаем временный search_space файл
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            search_space = {
                'signals': {
                    'zscore_threshold': {'low': 1.0, 'high': 2.0},
                    'zscore_exit': {'low': 0.0, 'high': 1.0}
                }
            }
            yaml.dump(search_space, f)
            search_space_path = f.name

        try:
            # Мокаем отбор пар
            with patch.object(Path, 'exists', return_value=True), \
                 patch('pandas.read_csv') as mock_read_csv:

                # Мокаем предварительно отобранные пары
                mock_pairs = pd.DataFrame({
                    'symbol_1': ['AAPL'],
                    'symbol_2': ['MSFT'],
                    'coint_pvalue': [0.01],
                    'half_life_days': [5.0]
                })
                mock_read_csv.return_value = mock_pairs

                objective = FastWalkForwardObjective(config_path, search_space_path)

                # Тест 1: Невалидные параметры с Optuna trial -> должен быть TrialPruned
                mock_trial = MagicMock()
                mock_trial.suggest_float.return_value = 1.5  # Возвращаем число вместо MagicMock
                mock_trial.set_user_attr = MagicMock()

                with patch('optimiser.fast_objective.validate_params') as mock_validate:
                    mock_validate.side_effect = ValueError("Тестовая ошибка валидации")

                    with pytest.raises(optuna.TrialPruned):
                        objective(mock_trial)

                # Тест 2: Невалидные параметры с обычным словарем -> должен быть PENALTY_SOFT
                invalid_params = {'zscore_threshold': -1.0}  # Невалидный параметр

                with patch('optimiser.fast_objective.validate_params') as mock_validate:
                    mock_validate.side_effect = ValueError("Тестовая ошибка валидации")

                    result = objective(invalid_params)
                    assert result == PENALTY_SOFT, f"Должен вернуть PENALTY_SOFT, получен: {result}"

                # Тест 3: Недостаточно сделок с Optuna trial -> должен быть TrialPruned
                mock_trial = MagicMock()
                mock_trial.suggest_float.return_value = 1.5  # Возвращаем число вместо MagicMock
                mock_trial.set_user_attr = MagicMock()

                with patch.object(objective, '_run_fast_backtest') as mock_backtest:
                    mock_backtest.return_value = {'sharpe_ratio_abs': 1.0, 'total_trades': 2}  # Мало сделок

                    with pytest.raises(optuna.TrialPruned):
                        objective(mock_trial)

                print("✅ Правильная стратегия pruning vs penalty")
            
        finally:
            os.unlink(config_path)
            os.unlink(search_space_path)

    def test_4_reproducibility_seeding_fixed(self):
        """
        КРИТИЧЕСКИЙ ТЕСТ 4: Воспроизводимость через seeding

        Проверяет что:
        - Optuna sampler использует seed
        - np.random.seed устанавливается
        - Результаты воспроизводимы
        """
        print("\n🔍 ТЕСТ 4: Воспроизводимость через seeding")

        # Проверяем что TPESampler создается с seed
        with patch('optuna.samplers.TPESampler') as mock_sampler:
            with patch('optuna.create_study') as mock_study:
                with patch('optimiser.run_optimization.FastWalkForwardObjective'):
                    with patch('os.path.exists', return_value=True):
                        with patch('builtins.open', create=True):
                            with patch('yaml.safe_load', return_value={'test': 'config'}):
                                # Вызываем функцию оптимизации с seed
                                run_optimization(n_trials=1, seed=42)

                                # Проверяем что TPESampler был вызван с правильным seed
                                mock_sampler.assert_called_once()
                                call_kwargs = mock_sampler.call_args[1]
                                assert 'seed' in call_kwargs, "TPESampler должен получить seed"
                                assert call_kwargs['seed'] == 42, f"Seed должен быть 42, получен: {call_kwargs['seed']}"

        print("✅ Воспроизводимость через seeding")

    def test_5_parameter_dependencies_fixed(self):
        """
        КРИТИЧЕСКИЙ ТЕСТ 5: Параметры-зависимости (zscore_exit < zscore_threshold)

        Проверяет что:
        - zscore_exit всегда меньше zscore_threshold
        - Невозможные комбинации отклоняются
        - Минимальный gap соблюдается
        """
        print("\n🔍 ТЕСТ 5: Параметры-зависимости исправлены")

        # Тест 1: Валидация параметров исправляет неправильные комбинации
        # validate_params автоматически исправляет параметры, а не выбрасывает ошибки
        invalid_params_1 = {'zscore_threshold': 1.0, 'zscore_exit': 1.5}  # exit > threshold
        corrected_1 = validate_params(invalid_params_1)
        assert corrected_1['zscore_exit'] < corrected_1['zscore_threshold'], \
            f"zscore_exit должен быть исправлен: {corrected_1}"

        invalid_params_2 = {'zscore_threshold': 1.0, 'zscore_exit': 1.0}  # exit == threshold
        corrected_2 = validate_params(invalid_params_2)
        assert corrected_2['zscore_exit'] < corrected_2['zscore_threshold'], \
            f"zscore_exit должен быть исправлен: {corrected_2}"

        # Тест 2: Валидные параметры проходят проверку
        valid_params = {'zscore_threshold': 2.0, 'zscore_exit': 0.5}
        validated = validate_params(valid_params)
        assert validated['zscore_threshold'] == 2.0
        assert validated['zscore_exit'] == 0.5

        # Тест 3: Проверяем логику в FastWalkForwardObjective
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            config = create_full_test_config()
            yaml.dump(config, f)
            config_path = f.name

        # Создаем временный search_space файл
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            search_space = {
                'signals': {
                    'zscore_threshold': {'low': 1.0, 'high': 2.0},
                    'zscore_exit': {'low': 0.0, 'high': 1.0}
                }
            }
            yaml.dump(search_space, f)
            search_space_path = f.name

        try:
            # Мокаем отбор пар
            with patch.object(Path, 'exists', return_value=True), \
                 patch('pandas.read_csv') as mock_read_csv:

                # Мокаем предварительно отобранные пары
                mock_pairs = pd.DataFrame({
                    'symbol_1': ['AAPL'],
                    'symbol_2': ['MSFT'],
                    'coint_pvalue': [0.01],
                    'half_life_days': [5.0]
                })
                mock_read_csv.return_value = mock_pairs

                objective = FastWalkForwardObjective(config_path, search_space_path)

                # Мокаем trial для проверки логики suggest
                mock_trial = MagicMock()
                mock_trial.suggest_float = MagicMock()

                # Устанавливаем threshold = 1.5
                def mock_suggest_side_effect(name, low, high):
                    if name == "zscore_threshold":
                        return 1.5
                    elif name == "zscore_exit":
                        # Проверяем что диапазон правильно ограничен
                        assert high < 1.5, f"zscore_exit high должен быть < 1.5, получен: {high}"
                        return 0.5
                    return 0.5

                mock_trial.suggest_float.side_effect = mock_suggest_side_effect

                # Вызываем генерацию параметров
                with patch.object(objective, '_run_fast_backtest', return_value={'sharpe_ratio_abs': 1.0, 'total_trades': 10}):
                    params = objective._suggest_parameters(mock_trial)

                    # Проверяем что параметры валидны
                    assert params['zscore_threshold'] > params['zscore_exit'], "threshold должен быть больше exit"
                    gap = params['zscore_threshold'] - params['zscore_exit']
                    assert gap >= 0.05, f"Gap должен быть >= 0.05, получен: {gap}"

        finally:
            os.unlink(config_path)
            os.unlink(search_space_path)

        print("✅ Параметры-зависимости исправлены")

    def test_6_no_duplicated_calculations_fixed(self):
        """
        КРИТИЧЕСКИЙ ТЕСТ 6: Отсутствие дублированных расчетов

        Проверяет что:
        - dd_penalty рассчитывается один раз
        - positive_days_bonus/penalty не дублируются
        - anti_churn_penalty рассчитывается корректно
        """
        print("\n🔍 ТЕСТ 6: Отсутствие дублированных расчетов")

        # Создаем временный конфиг
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            config = create_full_test_config()
            yaml.dump(config, f)
            config_path = f.name

        # Создаем временный search_space файл
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            search_space = {
                'signals': {
                    'zscore_threshold': {'low': 1.0, 'high': 2.0},
                    'zscore_exit': {'low': 0.0, 'high': 1.0}
                }
            }
            yaml.dump(search_space, f)
            search_space_path = f.name

        try:
            # Мокаем отбор пар
            with patch.object(Path, 'exists', return_value=True), \
                 patch('pandas.read_csv') as mock_read_csv:

                # Мокаем предварительно отобранные пары
                mock_pairs = pd.DataFrame({
                    'symbol_1': ['AAPL'],
                    'symbol_2': ['MSFT'],
                    'coint_pvalue': [0.01],
                    'half_life_days': [5.0]
                })
                mock_read_csv.return_value = mock_pairs

                objective = FastWalkForwardObjective(config_path, search_space_path)

                # Мокаем _run_fast_backtest для возврата контролируемых метрик
                test_metrics = {
                    'sharpe_ratio_abs': 1.5,
                    'total_trades': 20,
                    'max_drawdown': 0.15,  # 15% просадка
                    'positive_days_rate': 0.6  # 60% положительных дней
                }

                with patch.object(objective, '_run_fast_backtest', return_value=test_metrics):
                    # Вызываем целевую функцию
                    params = {'zscore_threshold': 2.0, 'zscore_exit': 0.5}
                    result = objective(params)

                    # Проверяем что результат разумный (не содержит дублированных штрафов)
                    assert isinstance(result, (int, float)), f"Результат должен быть числом, получен: {type(result)}"
                    assert result > 0, f"Результат должен быть положительным для хороших метрик, получен: {result}"

                    # Проверяем что результат в разумных пределах
                    # Sharpe 1.5 с небольшими штрафами должен дать результат около 1.3-1.4
                    assert 1.0 < result < 2.0, f"Результат должен быть в разумных пределах, получен: {result}"

        finally:
            os.unlink(config_path)
            os.unlink(search_space_path)

        print("✅ Отсутствие дублированных расчетов")

    def test_7_comprehensive_integration_test(self):
        """
        ИНТЕГРАЦИОННЫЙ ТЕСТ: Все исправления работают вместе

        Проверяет что все исправления не конфликтуют друг с другом
        и система работает корректно в целом.
        """
        print("\n🔍 ИНТЕГРАЦИОННЫЙ ТЕСТ: Все исправления работают вместе")

        # Создаем полный конфиг
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            config = create_full_test_config()
            yaml.dump(config, f)
            config_path = f.name

        # Создаем временный search_space файл
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            search_space = {
                'signals': {
                    'zscore_threshold': {'low': 1.5, 'high': 2.5},
                    'zscore_exit': {'low': 0.0, 'high': 1.0}
                }
            }
            yaml.dump(search_space, f)
            search_space_path = f.name

        try:
            # Мокаем все внешние зависимости
            with patch('optimiser.fast_objective.load_master_dataset') as mock_load, \
                 patch.object(Path, 'exists', return_value=True), \
                 patch('pandas.read_csv') as mock_read_csv:
                # Создаем реалистичные тестовые данные
                dates = pd.date_range('2023-01-01', periods=2000, freq='15min')
                mock_data = pd.DataFrame({
                    'timestamp': dates,
                    'symbol': ['AAPL'] * 1000 + ['MSFT'] * 1000,
                    'close': np.concatenate([
                        np.random.normal(100, 5, 1000),  # AAPL
                        np.random.normal(200, 10, 1000)  # MSFT
                    ])
                })
                mock_load.return_value = mock_data

                # Мокаем предварительно отобранные пары
                mock_pairs = pd.DataFrame({
                    'symbol_1': ['AAPL'],
                    'symbol_2': ['MSFT'],
                    'coint_pvalue': [0.01],
                    'half_life_days': [5.0]
                })
                mock_read_csv.return_value = mock_pairs

                objective = FastWalkForwardObjective(config_path, search_space_path)

                # Тестируем с валидными параметрами
                valid_params = {
                    'zscore_threshold': 2.0,
                    'zscore_exit': 0.5,
                    'rolling_window': 30
                }

                # Мокаем бэктест для возврата хороших результатов
                good_metrics = {
                    'sharpe_ratio_abs': 1.8,
                    'total_trades': 15,
                    'max_drawdown': 0.08,
                    'positive_days_rate': 0.65
                }

                with patch.object(objective, '_run_fast_backtest', return_value=good_metrics):
                    result = objective(valid_params)

                    # Проверяем что результат разумный
                    assert isinstance(result, (int, float)), "Результат должен быть числом"
                    assert result > 1.0, f"Хорошие метрики должны дать положительный результат, получен: {result}"

                    print(f"   Результат интеграционного теста: {result:.4f}")

        finally:
            os.unlink(config_path)
            os.unlink(search_space_path)

        print("✅ Все исправления работают вместе корректно")


if __name__ == "__main__":
    pytest.main([__file__, "-v", "-s"])
