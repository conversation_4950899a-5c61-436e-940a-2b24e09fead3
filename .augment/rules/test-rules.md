---
type: "agent_requested"
description: "Example description"
---
Эти правила описывают принципы, которых надо придерживаться при создании тестов. Цель: повышать надёжность без замедления пайплайна; добавлять минимальные по объёму тесты, покрывающие конкретные инварианты; приоритет unit, затем интеграции, e2e только при необходимости.

Принципы: детерминизм (фиксируй seed, не используй текущее время, сеть, внешние API); скорость (unit < 200 мс, integration < 3 с; дольше — помечать slow); изоляция (не трогать глобальные синглтоны/кэши; при необходимости — serial); малые данные (десятки–сотни строк, только нужные столбцы); идемпотентность в параллельном запуске.

Маркеры: smoke — быстрые сигнальные проверки; slow — долгие бэктесты/оптимизации/многопроцессность; serial — общий ресурс или глобальное состояние. По умолчанию — unit без маркеров; если превышены лимиты времени или есть общий ресурс — ставь соответствующий маркер.

Организация: имена файлов test_<module>__<feature>.py; одну идею проверяй параметризацией в одном файле, не плодить копии одинаковых ассершенов.

Фикстуры: использовать общие фикстуры для генерации маленьких датафреймов и rng; для файлов применять tmp_path; не использовать sleep, сеть и большие CSV.

Параллельность: тесты совместимы с pytest -n auto; не создавать процессы/пулы без нужды; если код многопоточный (Numba/BLAS) — ограничивать число потоков окружением; тесты с БД или глобальными кэшами помечать serial.

Оптимизация/гиперпараметры: минимизировать пространство поиска (2–3 параметра, узкие диапазоны), n_trials ≤ 3, фиксированный seed/сэмплер; интеграции с SQLite — только в serial и на tmp_path.

I/O: только tmp_path или in-memory; размер тестовых файлов до 1–5 КБ; не генерировать визуализации в тестах, проверять данные и инварианты.

Предупреждения: не использовать np.int/np.float/np.bool; собственные Deprecation чинить в коде, а не глушить; предупреждения сторонних либ можно игнорировать через pytest.ini.

Стиль и качество: один тест — один поведенческий инвариант; несколько вариантов — через параметризацию; ассерт с понятным сообщением; для массивов и таблиц использовать численные толерансы.

Дедупликация: перед добавлением теста искать похожие; если логика покрыта — расширять параметризацией вместо нового файла; в описании указывать, какие дубли заменены.

Приоритет покрытия: сначала утилиты и пограничные условия, затем ветки ошибок и исключения, далее небольшие интеграции, e2e — только в slow.

PR-чеклист: приложить durations для новых тестов; обоснование каждого теста (какой инвариант/регрессия); отсутствие дублей; корректные теги smoke/slow/serial; отсутствие сети и тяжёлого I/O; фиксированные сиды и толерансы.

Самопроверка: локально запускать быстрый набор (smoke и всё без slow/serial) в распараллеливании; отдельно — slow в распараллеливании; serial — на одном воркере.