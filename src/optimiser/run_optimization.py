#!/usr/bin/env python3
"""
Скрипт для запуска оптимизации стратегии с использованием Optuna.
"""

import sys
from pathlib import Path

# Добавляем корневую директорию проекта в PYTHONPATH
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

import optuna
import yaml
import logging
import argparse
import random
import numpy as np
import hashlib
import os
from typing import Optional, Dict, Any

# ИСПРАВЛЕНО: Правильные импорты
from src.optimiser.fast_objective import FastWalkForwardObjective
from src.coint2.utils.config import load_config

# Настройка логирования
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def _fmt4(x):
    """Безопасное форматирование числовых значений."""
    return f"{x:.4f}" if isinstance(x, (int, float)) else str(x)


def _convert_numpy_types(obj):
    """Рекурсивно преобразует numpy типы в python типы для безопасной сериализации."""
    if isinstance(obj, np.integer):
        return int(obj)
    elif isinstance(obj, np.floating):
        return float(obj)
    elif isinstance(obj, np.ndarray):
        return obj.tolist()
    elif isinstance(obj, dict):
        return {key: _convert_numpy_types(value) for key, value in obj.items()}
    elif isinstance(obj, list):
        return [_convert_numpy_types(item) for item in obj]
    else:
        return obj


def _compute_config_hash(base_config_path: str, search_space_path: str, preselected_pairs_path: str = None) -> str:
    """Вычисляет хэш конфигурации для проверки совместимости study."""
    hash_obj = hashlib.sha256()

    # Добавляем содержимое base config
    with open(base_config_path, 'r', encoding='utf-8') as f:
        hash_obj.update(f.read().encode('utf-8'))

    # Добавляем содержимое search space
    with open(search_space_path, 'r', encoding='utf-8') as f:
        hash_obj.update(f.read().encode('utf-8'))

    # ИСПРАВЛЕНО: Добавляем хэш списка пар для предотвращения смешивания разных наборов
    if preselected_pairs_path and os.path.exists(preselected_pairs_path):
        with open(preselected_pairs_path, 'r', encoding='utf-8') as f:
            hash_obj.update(f.read().encode('utf-8'))

    return hash_obj.hexdigest()[:16]  # Первые 16 символов для краткости


def run_optimization(n_trials: int = 200,
                    study_name: str = "pairs_strategy_v1",
                    storage_path: str = "outputs/studies/pairs_strategy_v1.db",
                    base_config_path: str = "configs/main_2024.yaml",
                    search_space_path: str = "configs/search_space.yaml",
                    n_jobs: int = -1,
                    seed: int = 42) -> bool:
    """Запуск оптимизации с валидацией параметров и обработкой ошибок.
    
    Args:
        n_trials: Количество trials для оптимизации
        study_name: Имя исследования
        storage_path: Путь к базе данных
        base_config_path: Путь к базовой конфигурации
        search_space_path: Путь к пространству поиска
        n_jobs: Количество параллельных процессов (-1 = все ядра)
        seed: Seed для воспроизводимости
        
    Returns:
        bool: True если оптимизация прошла успешно
    """
    # Валидация параметров
    if n_trials <= 0:
        logger.error(f"Некорректное количество trials: {n_trials}")
        return False
        
    if n_trials > 2000:
        logger.warning(f"Большое количество trials: {n_trials}. Рекомендуется <= 2000")
    
    # Проверка существования файлов конфигурации
    if not Path(base_config_path).exists():
        logger.error(f"Базовая конфигурация не найдена: {base_config_path}")
        return False
        
    if not Path(search_space_path).exists():
        logger.error(f"Пространство поиска не найдено: {search_space_path}")
        return False
    
    try:
        logger.info(f"🚀 Запуск оптимизации: {study_name}")
        logger.info(f"📊 Количество trials: {n_trials}")
        logger.info(f"💾 База данных: {storage_path}")

        # Устанавливаем глобальные сиды для воспроизводимости
        random.seed(seed)
        np.random.seed(seed)
        logger.info(f"🎲 Установлены глобальные сиды: {seed}")

        # Создаем директорию для хранения результатов
        outputs_dir = Path(storage_path).parent
        outputs_dir.mkdir(parents=True, exist_ok=True)

        # Создаем хранилище для любого типа базы данных
        def _make_storage(storage_path: str):
            """Создает RDBStorage для любого типа базы данных"""
            from optuna.storages import RDBStorage

            is_sqlalchemy_url = "://" in storage_path
            if is_sqlalchemy_url:
                # для postgres/mysql/remote sqlite и т.д.
                return RDBStorage(url=storage_path)

            # файловый SQLite
            return RDBStorage(
                url=f"sqlite:///{storage_path}",
                engine_kwargs={
                    "connect_args": {
                        "timeout": 600,  # 10 минут таймаут
                        "check_same_thread": False
                    },
                    "pool_pre_ping": True,
                    "pool_recycle": 300
                },
            )

        storage = _make_storage(storage_path)

        # ИСПРАВЛЕНО: Принудительное отключение параллельности для SQLite
        if storage_path.endswith('.db') or ('sqlite' in storage_path and '://' not in storage_path):
            if n_jobs != 1:
                logger.warning("⚠️  SQLite НЕ поддерживает безопасную параллельность!")
                logger.warning(f"   Принудительно устанавливаем n_jobs=1 (было: {n_jobs})")
                logger.warning("   Для параллельной оптимизации используйте PostgreSQL/MySQL")
                n_jobs = 1

        # Создаем objective-функцию
        logger.info("🎯 Создание БЫСТРОЙ objective-функции...")
        objective = FastWalkForwardObjective(
            base_config_path=base_config_path,
            search_space_path=search_space_path
        )

        # ИСПРАВЛЕНО: Правильная настройка sampler и pruner
        logger.info("📈 Создание study...")

        # ИСПРАВЛЕНО: Улучшенная логика startup trials - более консервативная для малых бюджетов
        n_startup_trials = max(5, min(15, n_trials // 10))

        # Нормализация n_jobs для безопасности
        if n_jobs is None or n_jobs < 1:
            n_jobs = os.cpu_count() or 1
        elif n_jobs == -1:
            n_jobs = os.cpu_count() or 1

        logger.info(f"🔧 Используем {n_jobs} потоков для оптимизации")

        sampler = optuna.samplers.TPESampler(
            seed=seed,  # ИСПРАВЛЕНО: используем переданный seed
            multivariate=True,
            group=True,  # Лучше для параллельных предложений
            constant_liar=(n_jobs > 1),  # Включаем при параллельности
            n_startup_trials=n_startup_trials
        )

        # КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: Промежуточные отчеты привязаны к walk-forward шагам, а не к парам
        # Это обеспечивает стабильность прюнинга независимо от состава пар
        # Если max_steps не задан, вычисляем количество шагов из временных границ
        if hasattr(objective.base_config.walk_forward, 'max_steps'):
            total_walk_forward_steps = objective.base_config.walk_forward.max_steps
        else:
            # Вычисляем количество шагов из конфигурации
            start_date = pd.to_datetime(objective.base_config.walk_forward.start_date)
            end_date = pd.to_datetime(getattr(objective.base_config.walk_forward, 'end_date',
                                            start_date + pd.Timedelta(days=objective.base_config.walk_forward.testing_period_days)))
            step_size_days = getattr(objective.base_config.walk_forward, 'step_size_days',
                                   objective.base_config.walk_forward.testing_period_days)

            total_days = (end_date - start_date).days
            total_walk_forward_steps = max(1, total_days // step_size_days)

        total_reports = max(1, total_walk_forward_steps)

        # При малом числе отчетов используем NopPruner
        if total_reports < 3:
            logger.info(f"🚫 Отключаем pruner: слишком мало отчетов ({total_reports})")
            pruner = optuna.pruners.NopPruner()
        else:
            # ИСПРАВЛЕНО: Улучшенная формула для n_warmup_steps
            n_warmup_steps = min(max(2, total_reports // 4), total_reports - 1)
            logger.info(f"✂️  Настройка pruner: {n_warmup_steps} warmup steps из {total_reports} отчетов")
            # ИСПРАВЛЕНО: Стандартизированные параметры pruner
            pruner = optuna.pruners.MedianPruner(
                n_warmup_steps=n_warmup_steps,
                interval_steps=5  # ИСПРАВЛЕНО: Соответствует обновленному INTERMEDIATE_REPORT_INTERVAL
            )

        study = optuna.create_study(
            study_name=study_name,
            storage=storage,
            load_if_exists=True,
            direction="maximize",
            sampler=sampler,
            pruner=pruner
        )

        # ИСПРАВЛЕНО: Проверяем совместимость конфигурации включая список пар
        preselected_pairs_path = "outputs/preselected_pairs.csv"
        config_hash = _compute_config_hash(base_config_path, search_space_path, preselected_pairs_path)
        logger.info(f"🔐 Хэш конфигурации (включая пары): {config_hash}")

        if len(study.trials) > 0:  # Если study уже существует
            existing_hash = study.user_attrs.get("config_hash")
            if existing_hash and existing_hash != config_hash:
                logger.error(f"❌ НЕСОВМЕСТИМАЯ КОНФИГУРАЦИЯ!")
                logger.error(f"   Существующий хэш: {existing_hash}")
                logger.error(f"   Новый хэш: {config_hash}")
                logger.error(f"   Измените study_name или используйте совместимую конфигурацию")
                raise ValueError(f"Study '{study_name}' создан с другой конфигурацией. "
                               f"Используйте другое имя study или совместимую конфигурацию.")
            elif not existing_hash:
                logger.warning("⚠️  Существующий study без хэша конфигурации - добавляем")

        # Сохраняем хэш конфигурации в study
        study.set_user_attr("config_hash", config_hash)
        study.set_user_attr("base_config_path", base_config_path)
        study.set_user_attr("search_space_path", search_space_path)

        # УЛУЧШЕНИЕ: Добавляем базовую точку для TPE
        if len(study.trials) == 0:  # Только если study пустой
            logger.info("🎯 Добавляем базовую точку для TPE...")
            base_params = {
                'zscore_threshold': 1.0,
                'zscore_exit': 0.3,
                'rolling_window': 25,
                'max_active_positions': 15,
                'risk_per_position_pct': 0.02,
                'max_position_size_pct': 0.1,
                'stop_loss_multiplier': 3.0,
                'time_stop_multiplier': 5.0,
                'cooldown_hours': 2,
                'commission_pct': 0.0004,
                'slippage_pct': 0.0005,
                'normalization_method': 'minmax',
                'min_history_ratio': 0.6
            }
            study.enqueue_trial(base_params)

        # Запускаем оптимизацию
        logger.info(f"⚡ Запуск оптимизации с {n_jobs} процессами...")
        study.optimize(
            objective,
            n_trials=n_trials,
            n_jobs=n_jobs,
            show_progress_bar=True,
            gc_after_trial=True  # ИСПРАВЛЕНО: Добавляем GC для управления памятью
        )

        # Проверяем результаты
        if len(study.trials) == 0:
            logger.error("Не было выполнено ни одного trial")
            return False
            
        completed_trials = [t for t in study.trials if t.state == optuna.trial.TrialState.COMPLETE]
        if len(completed_trials) == 0:
            logger.error("Не было завершено ни одного trial")
            return False

        logger.info("\n" + "="*50)
        logger.info("🎉 ОПТИМИЗАЦИЯ ЗАВЕРШЕНА")
        logger.info("="*50)
        # Исправленное логирование с детальными метриками
        best_trial = study.best_trial
        logger.info(f"Лучший композитный скор: {best_trial.value:.6f} (trial #{best_trial.number})")

        # ИСПРАВЛЕНО: Безопасное логирование метрик
        metrics = best_trial.user_attrs.get("metrics", {})
        if metrics:
            logger.info("Детальные метрики лучшего trial:")
            logger.info(f"  Sharpe ratio: {_fmt4(metrics.get('sharpe'))}")
            logger.info(f"  Max drawdown: {_fmt4(metrics.get('max_drawdown'))}")
            logger.info(f"  Win rate: {_fmt4(metrics.get('win_rate'))}")
            logger.info(f"  Total trades: {metrics.get('total_trades', 'N/A')}")
            logger.info(f"  DD penalty: {_fmt4(metrics.get('dd_penalty'))}")
            logger.info(f"  Win rate bonus: {_fmt4(metrics.get('win_rate_bonus'))}")
            logger.info(f"  Win rate penalty: {_fmt4(metrics.get('win_rate_penalty'))}")
        else:
            logger.info(f"Лучшее значение (композитный скор): {best_trial.value:.6f}")

        logger.info("Лучшие параметры:")
        for key, value in study.best_params.items():
            logger.info(f"  {key}: {value}")
        
        # Статистика
        failed_trials = [t for t in study.trials if t.state == optuna.trial.TrialState.FAIL]
        logger.info(f"\nСтатистика:")
        logger.info(f"  Всего trials: {len(study.trials)}")
        logger.info(f"  Завершено: {len(completed_trials)}")
        logger.info(f"  Неудачных: {len(failed_trials)}")

        # Сохраняем лучшую конфигурацию
        if not _save_best_config(study.best_params, base_config_path, study_name):
            logger.warning("Не удалось сохранить лучшую конфигурацию")
            
        return True
        
    except Exception as e:
        logger.error(f"Критическая ошибка оптимизации: {e}")
        return False


def _save_best_config(best_params: Dict[str, Any], base_config_path: str, study_name: str = "default") -> bool:
    """Сохраняет лучшую конфигурацию.
    
    Args:
        best_params: Лучшие параметры из оптимизации
        base_config_path: Путь к базовой конфигурации
        
    Returns:
        bool: True если сохранение прошло успешно
    """
    try:
        logger.info("💾 Сохранение лучшей конфигурации...")
        
        # Загружаем базовую конфигурацию
        best_cfg = load_config(base_config_path)
        
        # ИСПРАВЛЕНО: Правильное обновление параметров согласно search_space.yaml
        # Сигналы
        if "zscore_threshold" in best_params:
            best_cfg.backtest.zscore_threshold = best_params["zscore_threshold"]
        if "zscore_exit" in best_params:
            best_cfg.backtest.zscore_exit = best_params["zscore_exit"]
        
        # Управление риском
        if "stop_loss_multiplier" in best_params:
            best_cfg.backtest.stop_loss_multiplier = best_params["stop_loss_multiplier"]
        if "time_stop_multiplier" in best_params:
            best_cfg.backtest.time_stop_multiplier = best_params["time_stop_multiplier"]
        
        # Портфель
        if "max_active_positions" in best_params:
            best_cfg.portfolio.max_active_positions = best_params["max_active_positions"]
        if "risk_per_position_pct" in best_params:
            best_cfg.portfolio.risk_per_position_pct = best_params["risk_per_position_pct"]
        if "max_position_size_pct" in best_params:
            best_cfg.portfolio.max_position_size_pct = best_params["max_position_size_pct"]
        
        # Создаем директорию если не существует
        config_dir = Path("configs")
        config_dir.mkdir(exist_ok=True)
        
        # ИСПРАВЛЕНО: Уникальное имя файла конфигурации
        from datetime import datetime
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        best_config_path = f"configs/best_config__{study_name}__{timestamp}.yaml"
        config_dict = best_cfg.model_dump()

        # ИСПРАВЛЕНО: Безопасная сериализация с преобразованием numpy типов
        config_dict = _convert_numpy_types(config_dict)

        # Преобразуем Path объекты в строки
        if 'data_dir' in config_dict and hasattr(config_dict['data_dir'], '__fspath__'):
            config_dict['data_dir'] = str(config_dict['data_dir'])
        if 'results_dir' in config_dict and hasattr(config_dict['results_dir'], '__fspath__'):
            config_dict['results_dir'] = str(config_dict['results_dir'])

        with open(best_config_path, "w", encoding='utf-8') as f:
            yaml.dump(config_dict, f, default_flow_style=False, allow_unicode=True)
        
        logger.info(f"✅ Лучшая конфигурация сохранена: {best_config_path}")
        return True
        
    except Exception as e:
        logger.error(f"Ошибка при сохранении конфигурации: {e}")
        return False


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Запуск оптимизации Optuna")
    parser.add_argument("--n-trials", type=int, default=200,
                       help="Количество trials (по умолчанию: 200)")
    parser.add_argument("--study-name", default="pairs_strategy_v1",
                       help="Имя исследования")
    parser.add_argument("--storage-path", default="outputs/studies/pairs_strategy_v1.db",
                       help="Путь к базе данных")
    parser.add_argument("--base-config", default="configs/main_2024.yaml",
                       help="Путь к базовой конфигурации")
    parser.add_argument("--search-space", default="configs/search_space_fast.yaml",
                       help="Путь к пространству поиска")
    parser.add_argument("--n-jobs", type=int, default=-1,
                       help="Количество параллельных процессов (-1 = все ядра)")
    parser.add_argument("--seed", type=int, default=42,
                       help="Seed для воспроизводимости")
    args = parser.parse_args()

    # Валидация аргументов
    if args.n_trials <= 0:
        logger.error(f"Некорректное количество trials: {args.n_trials}")
        sys.exit(1)

    success = run_optimization(
        n_trials=args.n_trials,
        study_name=args.study_name,
        storage_path=args.storage_path,
        base_config_path=args.base_config,
        search_space_path=args.search_space,
        n_jobs=args.n_jobs,
        seed=args.seed
    )
    
    if not success:
        logger.error("Оптимизация завершилась с ошибкой")
        sys.exit(1)
    
    logger.info("🎉 Оптимизация завершена успешно!")