# configs/search_space_relaxed.yaml
# Ослабленное пространство поиска для генерации сделок

# Группа 1: Отбор пар - сильно ослабленные диапазоны
filters:
  ssd_top_n: {low: 25000, high: 100000, step: 25000}  # Меньше кандидатов
  kpss_pvalue_threshold: {low: 0.01, high: 0.1}       # Ослабляем стационарность
  coint_pvalue_threshold: {low: 0.2, high: 0.4}       # Сильно ослабляем коинтеграцию
  min_half_life_days: {low: 0.1, high: 1.0}           # Очень быстрые ревертирования
  max_half_life_days: {low: 30, high: 100}            # Расширяем диапазон
  min_mean_crossings: {low: 1, high: 5}               # Сильно ослабляем

# Группа 2: Сигналы и Тайминг - очень мягкие пороги
signals:
  zscore_threshold: {low: 0.8, high: 1.5}  # Очень низкие пороги входа
  zscore_exit: {low: 0.0, high: 0.5}       # Мягкий выход
  rolling_window: {low: 20, high: 60}      # Стандартные окна

# Группа 3: Управление Риском - мягкие стопы
risk_management:
  stop_loss_multiplier: {low: 3.0, high: 8.0}  # Широкие стоп-лоссы
  time_stop_multiplier: {low: 3.0, high: 10.0} # Долгое удержание
  cooldown_hours: {low: 1, high: 4}             # Короткий кулдаун

# Группа 4: Портфель - щедрые лимиты
portfolio:
  max_active_positions: {low: 2, high: 10, step: 2}  # Мало позиций
  risk_per_position_pct: {low: 0.02, high: 0.08}     # Высокий риск
  max_position_size_pct: {low: 0.05, high: 0.2}      # Большие позиции

# Группа 5: Издержки и реализм - низкие издержки
costs:
  commission_pct: {low: 0.0001, high: 0.0005}        # Низкие комиссии
  slippage_pct: {low: 0.0001, high: 0.0008}          # Низкое проскальзывание

# Группа 6: Нормализация данных - мягкие требования
normalization:
  normalization_method: ["minmax", "zscore"]  # Только стабильные методы
  min_history_ratio: {low: 0.3, high: 0.6}    # Мягкие требования к истории
