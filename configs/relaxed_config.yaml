# Relaxed configuration for increased trading activity
# Based on main_2024.yaml but with more lenient entry/exit thresholds

data_dir: "data_downloaded"
results_dir: "results"

# Настройки предобработки и нормализации данных
data_processing:
  normalization_method: "minmax"
  fill_method: "linear"
  min_history_ratio: 0.8
  handle_constant: true

portfolio:
  initial_capital: 10000.0
  risk_per_position_pct: 0.02  # Увеличиваем риск для большей активности
  max_active_positions: 20     # Увеличиваем количество позиций
  max_margin_usage: 0.5
  volatility_based_sizing: true
  volatility_lookback_hours: 24
  min_position_size_pct: 0.005
  max_position_size_pct: 0.03  # Увеличиваем максимальный размер
  volatility_adjustment_factor: 2.0

pair_selection:
  lookback_days: 60
  coint_pvalue_threshold: 0.5   # ОЧЕНЬ мягкий порог коинтеграции
  ssd_top_n: 25000             # Еще больше увеличиваем пул
  min_half_life_days: 0.1      # Минимальные требования
  max_half_life_days: 60
  min_mean_crossings: 1        # Минимальные требования
  adaptive_quantiles: false
  bar_minutes: 15
  liquidity_usd_daily: 10_000  # Минимальные требования к ликвидности
  max_bid_ask_pct: 2.0         # Очень мягкий спред
  max_avg_funding_pct: 0.2
  save_filter_reasons: true
  max_hurst_exponent: 1.0      # Отключаем фильтр Hurst
  kpss_pvalue_threshold: 0.0001 # Очень мягкая стационарность
  pvalue_top_n: 1000
  save_std_histogram: true
  enable_pair_tradeability_filter: false  # Отключаем фильтр торгуемости
  min_volume_usd_24h: 10_000   # Минимальный объем
  min_days_live: 1             # Минимальные дни жизни
  max_funding_rate_abs: 0.01
  max_tick_size_pct: 0.01
  max_half_life_hours: 1440.0

backtest:
  timeframe: "15min"
  rolling_window: 30
  zscore_threshold: 0.8        # ОЧЕНЬ мягкий порог входа
  zscore_entry_threshold: 0.8  # Дублируем для совместимости
  zscore_exit: 0.1             # Положительный выход
  stop_loss_multiplier: 3.0
  time_stop_multiplier: 2.0
  fill_limit_pct: 0.1
  commission_pct: 0.0004
  slippage_pct: 0.0005
  fee_maker: 0.0002
  fee_taker: 0.0004
  slippage_bps: 2.0
  half_spread_bps: 1.5
  slippage_stress_multiplier: 1.0
  always_model_slippage: true
  annualizing_factor: 365
  cooldown_hours: 2            # Сокращаем тайм-аут
  wait_for_candle_close: true
  min_spread_move_sigma: 0.01   # Максимально ослабляем требования к движению
  min_position_hold_minutes: 15    # Максимально сокращаем минимальное удержание
  anti_churn_cooldown_minutes: 15  # Максимально сокращаем защиту от пилы
  flat_zscore_threshold: 0.3
  pair_stop_loss_usd: 100.0
  pair_stop_loss_zscore: 3.0
  portfolio_daily_stop_pct: 0.03
  enable_funding_time_filter: false  # Отключаем фильтры времени
  enable_macro_event_filter: false
  enable_pair_quarantine: false
  enable_realistic_costs: true
  commission_rate_per_leg: 0.0004
  slippage_half_spread_multiplier: 2.0
  funding_cost_enabled: true
  beta_recalc_frequency_hours: 48
  beta_window_days_min: 30     # Сокращаем окно беты
  beta_window_days_max: 90
  use_ols_beta_sizing: true
  market_regime_detection: false  # Отключаем определение режимов
  structural_break_protection: false  # Отключаем защиту от сдвигов
  regime_check_frequency: 192
  use_market_regime_cache: true
  adf_check_frequency: 5376
  cache_cleanup_frequency: 1000
  lazy_adf_threshold: 0.1
  hurst_neutral_band: 0.05
  vr_neutral_band: 0.2
  n_jobs: -1
  use_exponential_weighted_correlation: true
  ew_correlation_alpha: 0.1
  use_kelly_sizing: false      # Отключаем Kelly для простоты
  adaptive_thresholds: false   # Отключаем адаптивные пороги

walk_forward:
  enabled: true
  start_date: "2024-01-15"
  end_date: "2024-02-08"
  training_period_days: 10
  testing_period_days: 3
  step_size_days: 3
  min_training_samples: 500
  refit_frequency: "weekly"

normalization:
  method: "log_returns"
  fit_on_training_only: true
  prevent_refit_on_test: true
  log_returns_base: "natural"

online_statistics:
  enabled: true
  volatility_method: "ewm"
  ewm_alpha: 0.05
  kelly_lookback_trades: 50
  adaptive_threshold_lookback: 96
  beta_recalc_online: true

signal_shift:
  enabled: true
  shift_periods: 1
  skip_last_bar: true
  signal_delay_minutes: 0

max_shards: null

filter_params:
  min_beta: 0.01               # Минимальные фильтры
  max_beta: 50.0
  min_half_life_days: 0.1
  max_half_life_days: 500
  max_hurst_exponent: 1.0      # Отключаем фильтр Hurst
  min_mean_crossings: 1

logging:
  trade_details: true
  debug_level: "INFO"