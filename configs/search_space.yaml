# configs/search_space.yaml

# Группа 1: Отбор пар - исправленные диапазоны для генерации сделок
filters:
  ssd_top_n: {low: 50000, high: 150000, step: 25000}  # Еще больше кандидатов
  kpss_pvalue_threshold: {low: 0.005, high: 0.08}     # Более либеральная стационарность
  coint_pvalue_threshold: {low: 0.1, high: 0.3}       # Более либеральная коинтеграция
  min_half_life_days: {low: 0.2, high: 2.0}           # Более быстрые ревертирования
  max_half_life_days: {low: 20, high: 80}             # Расширяем диапазон
  min_mean_crossings: {low: 2, high: 8}               # ИСПРАВЛЕНО: уменьшили максимум

# Группа 2: Сигналы и Тайминг - исправленные диапазоны
signals:
  zscore_threshold: {low: 1.0, high: 2.2}  # ИСПРАВЛЕНО: уменьшили максимум
  zscore_exit: {low: -0.5, high: 0.5}      # ИСПРАВЛЕНО: расширили диапазон
  rolling_window: {low: 25, high: 80}      # ИСПРАВЛЕНО: увеличили минимум

# Группа 3: Управление Риском
risk_management:
  stop_loss_multiplier: {low: 2.0, high: 5.0}  # Расширяем диапазон
  time_stop_multiplier: {low: 1.0, high: 4.0}  # Больше вариантов
  cooldown_hours: {low: 1, high: 8}            # Оптимизируем кулдаун

# Группа 4: Портфель
portfolio:
  max_active_positions: {low: 3, high: 30, step: 3}  # Больше диапазон
  risk_per_position_pct: {low: 0.01, high: 0.05}     # Расширяем риск
  max_position_size_pct: {low: 0.03, high: 0.15}     # Больше размеры

# Группа 5: Издержки и реализм
costs:
  commission_pct: {low: 0.0002, high: 0.0008}        # Оптимизируем комиссии
  slippage_pct: {low: 0.0003, high: 0.001}           # Оптимизируем проскальзывание

# Группа 6: Нормализация данных
normalization:
  normalization_method: ["minmax", "zscore", "log_returns"]  # Разные методы
  min_history_ratio: {low: 0.4, high: 0.7}                   # ИСПРАВЛЕНО: еще больше ослабили требования