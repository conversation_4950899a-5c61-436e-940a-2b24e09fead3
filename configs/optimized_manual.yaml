data_dir: "data_downloaded"
results_dir: "results"

# Настройки предобработки и нормализации данных
data_processing:
  normalization_method: "minmax"    # Метод нормализации: minmax, zscore, log_returns
  fill_method: "linear"             # Метод заполнения пропусков: ffill, linear, none
  min_history_ratio: 0.8            # Минимальная доля непропущенных значений
  handle_constant: true             # Обрабатывать ли символы с постоянной ценой
portfolio:
  initial_capital: 10000.0      # Капитал $10,000
  risk_per_position_pct: 0.015  # Риск 1.5% на позицию
  max_active_positions: 15     # Максимум 15 позиций для лучшей диверсификации
  max_margin_usage: 0.5         # Максимальное использование маржи 50%
  # Динамическое позиционирование на основе волатильности
  volatility_based_sizing: true # Включить динамическое позиционирование
  volatility_lookback_hours: 24 # Окно для расчета волатильности (24 часа)
  min_position_size_pct: 0.005  # Минимальный размер позиции (0.5%)
  max_position_size_pct: 0.02   # Максимальный размер позиции (2%)
  volatility_adjustment_factor: 2.0 # Коэффициент корректировки на волатильность

pair_selection:
  lookback_days: 60             # Коинтеграцию считать по последним 60 суткам
  coint_pvalue_threshold: 0.05  # Оставляем строгий p-value
  ssd_top_n: 15000              # Увеличиваем пул кандидатов
  min_half_life_days: 1.0       # ИСПРАВЛЕНИЕ: Возвращаем к 1 дню
  max_half_life_days: 21        # ИСПРАВЛЕНИЕ: Возвращаем к 21 дню
  min_mean_crossings: 8         # ИСПРАВЛЕНИЕ: Возвращаем к 8
  # Новые параметры фильтрации пар
  adaptive_quantiles: false     # Используем абсолютные пороги std
  bar_minutes: 15               # Размер бара в минутах
  liquidity_usd_daily: 100_000      # Еще больше снижаем требования к ликвидности
  max_bid_ask_pct: 0.5          # Ослабляем с 0.2 до 0.5
  max_avg_funding_pct: 0.05     # Ослабляем с 0.03 до 0.05
  save_filter_reasons: true
  max_hurst_exponent: 0.5       # ИСПРАВЛЕНИЕ: Возвращаем к классическому значению
  # Параметры min_abs_spread_mult и cost_filter удалены
  kpss_pvalue_threshold: 0.005  # Ослабляем с 0.01 до 0.005
  pvalue_top_n: 500             # Увеличиваем с 200 до 500
  # Дополнительные параметры
  save_std_histogram: true     # Сохранять гистограмму σ
  
  # NEW: Фильтр допустимости пар (ослабленный)
  enable_pair_tradeability_filter: true
  min_volume_usd_24h: 200_000       # Снижаем с 1M до 200K USD
  min_days_live: 7                  # Снижаем с 30 до 7 дней
  max_funding_rate_abs: 0.001       # Ослабляем с 0.0003 до 0.001
  max_tick_size_pct: 0.002          # Ослабляем с 0.0005 до 0.002
  max_half_life_hours: 720.0        # Увеличиваем с 336 до 720 часов (30 дней)

backtest:
  timeframe: "15min"
  rolling_window: 30
  zscore_threshold: 0.5          # ИСПРАВЛЕНО: Агрессивно снижаем порог для максимальной активности
  zscore_entry_threshold: 0.5    # Дублируем для совместимости
  zscore_exit: 0.1               # ИСПРАВЛЕНО: Более агрессивный выход для увеличения оборота
  stop_loss_multiplier: 3.0     # Стоп-лосс при 3 std
  time_stop_multiplier: 2.0
  # take_profit_multiplier: 0.2 # ИСПРАВЛЕНИЕ: Убираем агрессивный take-profit
  fill_limit_pct: 0.1

  # Обязательные параметры издержек (для совместимости)
  commission_pct: 0.0004        # Общая комиссия (0.04%)
  slippage_pct: 0.0005          # Общее проскальзывание (0.05%)

  # Детализированные параметры издержек
  fee_maker: 0.0002             # Комиссия мейкера (0.02%)
  fee_taker: 0.0004             # Комиссия тейкера (0.04%)
  slippage_bps: 2.0             # Проскальзывание в базисных пунктах
  half_spread_bps: 1.5          # Половина спреда в базисных пунктах
  slippage_stress_multiplier: 1.0  # Множитель для стресс-тестирования
  always_model_slippage: true   # Всегда моделировать проскальзывание
  annualizing_factor: 365
  cooldown_hours: 4             # ИСПРАВЛЕНИЕ: Увеличиваем тайм-аут между сделками
  wait_for_candle_close: true   # Ждем закрытия свечи перед входом

  # NEW: Улучшенные правила входа/выхода
  min_spread_move_sigma: 0.5        # ИСПРАВЛЕНИЕ: Требуем значительное движение спреда
  min_position_hold_minutes: 240    # ИСПРАВЛЕНИЕ: Минимальное удержание 4 часа
  anti_churn_cooldown_minutes: 240  # ИСПРАВЛЕНИЕ: Защита от "пилы" - 4 часа
  flat_zscore_threshold: 0.5        # Порог "плоского" z-score для отслеживания спреда
  
  # NEW: Улучшенные стоп-лоссы
  pair_stop_loss_usd: 75.0          # Пара-стоп-лосс 75 USD
  pair_stop_loss_zscore: 3.0        # Z-score стоп-лосс при 3 сигма
  portfolio_daily_stop_pct: 0.02    # Портфельный стоп-аут 2%
  
  # NEW: Временные фильтры
  enable_funding_time_filter: true
  funding_blackout_minutes: 30      # 30 мин до/после фандинга
  funding_reset_hours: [0, 8, 16]   # UTC часы сброса фандинга
  enable_macro_event_filter: true
  macro_blackout_minutes: 30        # 30 мин после макроданных
  
  # NEW: Карантин "сломанных" пар
  enable_pair_quarantine: false
  quarantine_pnl_threshold_sigma: 3.0    # -3 сигма PnL порог
  quarantine_drawdown_threshold_pct: 0.08 # 8% просадка порог
  quarantine_period_days: 7              # 7 дней карантина
  quarantine_rolling_window_days: 30     # 30 дней окно для статистики
  
  # NEW: Реалистичные издержки
  enable_realistic_costs: true
  commission_rate_per_leg: 0.0004        # 0.04% за ногу
  slippage_half_spread_multiplier: 2.0   # 2x полуспред для проскальзывания
  funding_cost_enabled: true
  
  # NEW: Улучшенное позиционирование с пересчетом беты
  beta_recalc_frequency_hours: 48        # Пересчет беты каждые 48 часов
  beta_window_days_min: 60               # Минимум 60 дней для беты
  beta_window_days_max: 120              # Максимум 120 дней для беты
  use_ols_beta_sizing: true              # Использовать OLS бету для размера позиций
  
  # Определение рыночных режимов
  market_regime_detection: true     # Включить определение рыночных режимов для защиты
  hurst_window: 720                 # 30 дней для 15-минутных данных (30*24*4/3 = 720)
  hurst_trending_threshold: 0.5     # H > 0.5 = trending режим
  variance_ratio_window: 480        # 20 дней для Variance Ratio Test
  variance_ratio_trending_min: 1.2  # VR > 1.2 = trending
  variance_ratio_mean_reverting_max: 0.8  # VR < 0.8 = mean-reverting
  
  # Защита от структурных сдвигов
  structural_break_protection: true  # Включить защиту от структурных сдвигов
  cointegration_test_frequency: 2688 # Каждые 7 дней (7*24*4*4 = 2688 15-минутных баров)
  adf_pvalue_threshold: 0.05        # p-value > 0.05 → закрыть позицию
  exclusion_period_days: 30         # Исключить пару на 30 дней
  max_half_life_days: 14           
  min_correlation_threshold: 0.6    # Корреляция < 0.6 → закрыть позицию
  correlation_window: 720           # 30 дней для скользящей корреляции
  
  # Performance optimization parameters
  regime_check_frequency: 192       # Проверять режим каждые 192 бара (48 часов для 15-мин данных) - УСКОРЕНИЕ
  use_market_regime_cache: true     # Включить кэширование для Hurst/VR расчетов - УСКОРЕНИЕ
  adf_check_frequency: 5376         # ADF тесты каждые 14 дней (14*24*4*4 = 5376 15-минутных баров) - УСКОРЕНИЕ
  cache_cleanup_frequency: 1000     # Очищать кэш каждые 1000 баров
  lazy_adf_threshold: 0.1           # Запускать ADF только при изменении корреляции > 0.1 - УСКОРЕНИЕ
  hurst_neutral_band: 0.05          # Нейтральная зона вокруг 0.5 для Hurst - СТАБИЛЬНОСТЬ
  vr_neutral_band: 0.2              # Нейтральная зона вокруг 1.0 для VR - СТАБИЛЬНОСТЬ
  n_jobs: -1                        # Количество параллельных процессов (-1 = все доступные ядра) - УСКОРЕНИЕ
  use_exponential_weighted_correlation: true  # Использовать экспоненциально взвешенную корреляцию - ТОЧНОСТЬ
  ew_correlation_alpha: 0.1         # Параметр сглаживания для EW-корреляции (0.05-0.2) - ТОЧНОСТЬ
  
  # NEW: Enhanced risk management parameters
  use_kelly_sizing: true        # Использовать Kelly criterion для размера позиций
  max_kelly_fraction: 0.25      # Максимальная доля Kelly (25%)
  volatility_lookback: 96       # Окно для расчета волатильности (24 часа для 15-мин данных)
  adaptive_thresholds: true     # Адаптивные пороги входа на основе волатильности
  var_confidence: 0.05          # Уровень доверия для VaR (5%)
  max_var_multiplier: 3.0       # Максимальный множитель порога при высокой волатильности

walk_forward:
  enabled: true                 # Включить walk-forward тестирование
  start_date: "2024-01-15"      # Начинаем с середины января (есть данные)
  end_date: "2024-02-08"        # Заканчиваем последней доступной датой
  training_period_days: 10      # Сокращаем период тренировки для быстрого теста
  testing_period_days: 3        # Сокращаем период тестирования
  step_size_days: 3             # Шаг сдвига окна
  min_training_samples: 500     # Минимум образцов для обучения
  refit_frequency: "weekly"     # Частота переобучения: daily, weekly, monthly
  
# Нормализация fit/transform
normalization:
  method: "log_returns"         # log_returns, minmax, zscore
  fit_on_training_only: true    # Fit только на train, transform на train+test
  prevent_refit_on_test: true   # Запрет повторного fit на test данных
  log_returns_base: "natural"   # natural, log10, log2
  
# Онлайновые статистики
online_statistics:
  enabled: true                 # Использовать только исторические данные
  volatility_method: "ewm"      # ewm, rolling для расчета волатильности
  ewm_alpha: 0.05              # Параметр сглаживания для EWM
  kelly_lookback_trades: 50     # Количество последних сделок для Kelly
  adaptive_threshold_lookback: 96  # Окно для адаптивных порогов (24 часа)
  beta_recalc_online: true      # Пересчет беты только на исторических данных
  
# Сдвиг сигналов
signal_shift:
  enabled: true                 # Включить сдвиг сигналов
  shift_periods: 1              # Сигналы на баре i, исполнение на i+1
  skip_last_bar: true           # Не торговать последний бар
  signal_delay_minutes: 0       # Дополнительная задержка в минутах

max_shards: null

filter_params:
  min_beta: 0.1
  max_beta: 10.0
  min_half_life_days: 1
  max_half_life_days: 252
  max_hurst_exponent: 0.5
  min_mean_crossings: 10

logging:
  trade_details: true           # Логирование деталей сделок
  debug_level: "INFO"           # Уровень логирования
