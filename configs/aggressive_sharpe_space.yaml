# Агрессивное пространство поиска для достижения Sharpe > 1
# Расширенные параметры с фокусом на высокую доходность

# Основные торговые сигналы
strategy:
  # Пороги входа - более агрессивные
  entry_threshold:
    type: float
    low: 1.0
    high: 4.0
    
  exit_threshold:
    type: float
    low: 0.05
    high: 1.5
    
  stop_loss_threshold:
    type: float
    low: 2.5
    high: 8.0
    
  # Размер позиций - более агрессивный
  max_position_size:
    type: float
    low: 0.1
    high: 0.5
    
  # Временные окна
  lookback_window:
    type: int
    low: 10
    high: 200
    
  half_life_window:
    type: int
    low: 5
    high: 100
    
  # Дополнительные параметры для высокой доходности
  momentum_factor:
    type: float
    low: 0.5
    high: 2.0
    
  volatility_adjustment:
    type: float
    low: 0.8
    high: 1.5

# Отбор пар - более селективный
pair_selection:
  # Коинтеграция
  pvalue_threshold:
    type: float
    low: 0.001
    high: 0.05
    
  # Half-life - более строгие требования
  min_half_life_days:
    type: float
    low: 0.2
    high: 3.0
    
  max_half_life_days:
    type: float
    low: 3.0
    high: 15.0
    
  # Активность пар
  min_mean_crossings:
    type: int
    low: 10
    high: 50
    
  # Hurst для mean reversion
  max_hurst_exponent:
    type: float
    low: 0.3
    high: 0.55
    
  # KPSS для стационарности
  kpss_pvalue_threshold:
    type: float
    low: 0.01
    high: 0.2
    
  # Нормализация
  normalization_method:
    type: categorical
    choices: ["minmax", "log_returns"]
    
  # Beta диапазон
  min_beta:
    type: float
    low: 0.3
    high: 1.0
    
  max_beta:
    type: float
    low: 2.0
    high: 5.0

# Управление рисками - агрессивное
risk_management:
  # Стоп-лоссы
  stop_loss_multiplier:
    type: float
    low: 2.0
    high: 6.0
    
  # Временные стопы
  time_stop_hours:
    type: int
    low: 6
    high: 72
    
  # Максимальная просадка
  max_drawdown:
    type: float
    low: 0.15
    high: 0.4
    
  # Cooldown между сделками
  cooldown_hours:
    type: int
    low: 1
    high: 24
    
  # Динамическое управление размером
  dynamic_sizing:
    type: categorical
    choices: ["fixed", "volatility", "kelly", "risk_parity"]

# Портфельные параметры
portfolio:
  # Риск на позицию
  risk_per_position_pct:
    type: float
    low: 0.02
    high: 0.1
    
  # Максимальный размер позиции
  max_position_size_pct:
    type: float
    low: 0.1
    high: 0.4
    
  # Количество активных позиций
  max_active_positions:
    type: int
    low: 3
    high: 20
    
  # Корреляционные ограничения
  max_correlation:
    type: float
    low: 0.3
    high: 0.8
    
  # Ребалансировка
  rebalance_frequency_hours:
    type: int
    low: 4
    high: 48

# Издержки и проскальзывание
costs:
  commission_pct:
    type: float
    low: 0.0001
    high: 0.001
    
  slippage_pct:
    type: float
    low: 0.0001
    high: 0.0005
    
  funding_rate_pct:
    type: float
    low: 0.0
    high: 0.01

# Дополнительные фильтры для качества
quality_filters:
  # Минимальный объем торгов
  min_volume_usd:
    type: float
    low: 500000
    high: 5000000
    
  # Максимальный спред
  max_bid_ask_spread_pct:
    type: float
    low: 0.05
    high: 0.3
    
  # Минимальная история
  min_history_ratio:
    type: float
    low: 0.7
    high: 0.95