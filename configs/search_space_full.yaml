# Полное пространство поиска включая отбор пар
# Оптимизируем ВСЕ ключевые параметры pairs trading

# ========================================
# 1. ОТБОР ПАР (PAIR SELECTION)
# ========================================
pair_selection:
  # Коинтеграция
  coint_pvalue_threshold:
    low: 0.01      # Строгий отбор
    high: 0.15     # Либеральный отбор
  
  lookback_days:
    low: 30        # Короткая история
    high: 90       # Длинная история
    step: 15
  
  # Hurst exponent (mean reversion)
  max_hurst_exponent:
    low: 0.3       # Сильная mean reversion
    high: 0.6      # Слабая mean reversion
  
  # Half-life (скорость возврата к среднему)
  min_half_life_days:
    low: 0.5       # Быстрый возврат
    high: 3.0      # Медленный возврат
  
  max_half_life_days:
    low: 10        # Короткий максимум
    high: 45       # Длинный максимум
  
  # Mean crossings (частота пересечений)
  min_mean_crossings:
    low: 3         # Мало пересечений
    high: 15       # Много пересечений
    step: 2
  
  # KPSS test (стационарность)
  kpss_pvalue_threshold:
    low: 0.001     # Строгая стационарность
    high: 0.01     # Мягкая стационарность
  
  # Количество пар
  ssd_top_n:
    low: 100       # Мало пар, высокое качество
    high: 1000     # Много пар, разнообразие
    step: 100
  
  pvalue_top_n:
    low: 50        # Топ пары
    high: 500      # Широкий выбор
    step: 50

# ========================================
# 2. ТОРГОВЫЕ СИГНАЛЫ
# ========================================
signals:
  zscore_threshold:
    low: 0.8
    high: 2.5
  
  zscore_exit:
    low: 0.0       # Возврат к среднему
    high: 0.8      # Частичный возврат
  
  rolling_window:
    low: 20
    high: 80
    step: 5

# ========================================
# 3. УПРАВЛЕНИЕ ПОРТФЕЛЕМ
# ========================================
portfolio:
  max_active_positions:
    low: 5
    high: 25
    step: 5
  
  risk_per_position_pct:
    low: 0.005     # Консервативный
    high: 0.03     # Агрессивный
  
  max_position_size_pct:
    low: 0.05      # Маленькие позиции
    high: 0.25     # Большие позиции
  
  # Корреляция между позициями
  max_correlation_threshold:
    low: 0.3       # Низкая корреляция
    high: 0.8      # Высокая корреляция

# ========================================
# 4. РИСК-МЕНЕДЖМЕНТ
# ========================================
risk_management:
  stop_loss_multiplier:
    low: 2.0       # Тайтовый стоп
    high: 8.0      # Широкий стоп
  
  time_stop_multiplier:
    low: 2.0       # Быстрый выход
    high: 10.0     # Долгое удержание
  
  cooldown_hours:
    low: 1         # Быстрое переоткрытие
    high: 12       # Долгий кулдаун
    step: 1

# ========================================
# 5. ИСПОЛНЕНИЕ И COSTS
# ========================================
execution:
  commission_pct:
    low: 0.0001    # Низкие комиссии
    high: 0.0008   # Высокие комиссии
  
  slippage_pct:
    low: 0.0001    # Низкое проскальзывание
    high: 0.0008   # Высокое проскальзывание

# ========================================
# 6. ОБРАБОТКА ДАННЫХ
# ========================================
data_processing:
  normalization_method:
    - minmax
    - zscore
    - robust
  
  min_history_ratio:
    low: 0.4       # Мало истории
    high: 0.9      # Много истории

# ========================================
# 7. ФИЛЬТРЫ КАЧЕСТВА
# ========================================
quality_filters:
  # Ликвидность
  min_volume_usd_24h:
    low: 100000    # $100K
    high: 1000000  # $1M
  
  # Спреды
  max_bid_ask_pct:
    low: 0.001     # Узкие спреды
    high: 0.01     # Широкие спреды
  
  # Funding rates
  max_avg_funding_pct:
    low: 0.0001    # Низкий funding
    high: 0.001    # Высокий funding
