[pytest]
markers =
    slow: долгие интеграционные/оптимизационные тесты
    serial: нельзя параллелить (например, SQLite/глобальные кэши)
    smoke: очень быстрые ключевые проверки
    integration: интеграционные тесты
    performance: тесты производительности
    memory: тесты использования памяти
    cache: тесты кэширования
    concurrent: тесты конкурентности

addopts = -q --durations=25 -m "not slow and not serial"

testpaths = tests

python_files = test_*.py
python_classes = Test*
python_functions = test_*

# Фильтры для предупреждений
filterwarnings =
    # Скрываем шум от сторонних либ; своё чиним
    ignore::DeprecationWarning:optuna\..*
    ignore::DeprecationWarning:pandas\..*
    ignore::DeprecationWarning:numpy\..*
    ignore::PendingDeprecationWarning:optuna\..*
    ignore::PendingDeprecationWarning:pandas\..*
    ignore::PendingDeprecationWarning:numpy\..*
    # Строгий режим для собственного кода (будет включен после исправления numpy алиасов)
    # error::DeprecationWarning:src\..*
